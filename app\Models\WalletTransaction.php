<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WalletTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'vendor_wallet_id',
        'type',
        'amount',
        'status',
        'description',
        'order_id',
        'withdrawal_id',
        'balance_after',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * Transaction types
     */
    const TYPE_EARNING = 'earning';
    const TYPE_WITHDRAWAL = 'withdrawal';
    const TYPE_FEE = 'fee';
    const TYPE_RELEASE = 'release';
    const TYPE_REFUND = 'refund';

    /**
     * Transaction statuses
     */
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the wallet that owns the transaction
     */
    public function wallet()
    {
        return $this->belongsTo(VendorWallet::class, 'vendor_wallet_id');
    }

    /**
     * Get the order associated with the transaction
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the withdrawal associated with the transaction
     */
    public function withdrawal()
    {
        return $this->belongsTo(Withdrawal::class);
    }

    /**
     * Check if transaction is a credit (positive amount)
     */
    public function isCredit(): bool
    {
        return $this->amount > 0;
    }

    /**
     * Check if transaction is a debit (negative amount)
     */
    public function isDebit(): bool
    {
        return $this->amount < 0;
    }

    /**
     * Get formatted amount with currency symbol
     */
    public function getFormattedAmountAttribute(): string
    {
        $symbol = config('ecommerce.commission.currency_symbol', '₦');
        $amount = number_format(abs($this->amount), 2);
        
        if ($this->isDebit()) {
            return "-{$symbol}{$amount}";
        }
        
        return "{$symbol}{$amount}";
    }

    /**
     * Get transaction type label
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            self::TYPE_EARNING => 'Earning',
            self::TYPE_WITHDRAWAL => 'Withdrawal',
            self::TYPE_FEE => 'Platform Fee',
            self::TYPE_RELEASE => 'Earnings Released',
            self::TYPE_REFUND => 'Refund',
            default => ucfirst($this->type),
        };
    }

    /**
     * Get transaction status label
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_CANCELLED => 'Cancelled',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get status badge class for UI
     */
    public function getStatusBadgeClass(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'badge-warning',
            self::STATUS_COMPLETED => 'badge-success',
            self::STATUS_FAILED => 'badge-danger',
            self::STATUS_CANCELLED => 'badge-secondary',
            default => 'badge-secondary',
        };
    }

    /**
     * Get type badge class for UI
     */
    public function getTypeBadgeClass(): string
    {
        return match($this->type) {
            self::TYPE_EARNING => 'badge-success',
            self::TYPE_WITHDRAWAL => 'badge-primary',
            self::TYPE_FEE => 'badge-warning',
            self::TYPE_RELEASE => 'badge-info',
            self::TYPE_REFUND => 'badge-danger',
            default => 'badge-secondary',
        };
    }

    /**
     * Scope for filtering by type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for credit transactions
     */
    public function scopeCredits($query)
    {
        return $query->where('amount', '>', 0);
    }

    /**
     * Scope for debit transactions
     */
    public function scopeDebits($query)
    {
        return $query->where('amount', '<', 0);
    }

    /**
     * Scope for recent transactions
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get valid transaction types
     */
    public static function getValidTypes(): array
    {
        return [
            self::TYPE_EARNING,
            self::TYPE_WITHDRAWAL,
            self::TYPE_FEE,
            self::TYPE_RELEASE,
            self::TYPE_REFUND,
        ];
    }

    /**
     * Get valid transaction statuses
     */
    public static function getValidStatuses(): array
    {
        return [
            self::STATUS_PENDING,
            self::STATUS_COMPLETED,
            self::STATUS_FAILED,
            self::STATUS_CANCELLED,
        ];
    }
}
