/**
 * Vendor Dashboard JavaScript
 *
 * This file handles all the interactive elements and data visualization
 * for the Brandify vendor dashboard including charts and map functionality.
 */

console.log('vendor-dashboard.js loaded');

// Global chart instances
let revenueChart, categoryChart, customerChart;

// Main initialization function for all dashboard widgets
function initDashboardWidgets() {
    console.log('Initializing dashboard widgets...');

    // Initialize charts
    initDashboardCharts();

    // Initialize Nigeria Map
    initNigeriaMapWidget();

    // Set up event listeners for timeframe switching
    setupChartTimeframeListeners();
    setupMapTimeframeListeners();

    console.log('Dashboard widgets initialized');
}

// Initialize all dashboard charts with properly formatted data
function initDashboardCharts() {
    console.log('Initializing dashboard charts...');

    // Check if chart creation functions are available
    if (typeof createRevenueChart === 'undefined') {
        console.error('createRevenueChart function not found! Make sure chart-config.js is loaded.');
        return;
    }

    // Initialize charts with empty data initially
    console.log('Creating revenue chart...');
    revenueChart = createRevenueChart('revenueChart');
    console.log('Creating category chart...');
    categoryChart = createCategoryChart('categoryChart');
    console.log('Creating customer chart...');
    customerChart = createCustomerGrowthChart('customerChart');

    console.log('Charts created, fetching data...');
    // Fetch initial data (default: last 6 months)
    fetchChartData('6months');
}

// Set up event listeners for chart timeframe switching
function setupChartTimeframeListeners() {
    const timeframeElements = document.querySelectorAll('.chart-timeframe');
    if (!timeframeElements.length) return;
    
    timeframeElements.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active state in dropdown
            document.querySelectorAll('.chart-timeframe').forEach(el => el.classList.remove('active'));
            this.classList.add('active');
            
            // Update dropdown button text
            const period = this.getAttribute('data-period');
            let buttonText = 'Last 6 Months';
            if (period === 'week') buttonText = 'This Week';
            else if (period === 'month') buttonText = 'This Month';
            else if (period === 'year') buttonText = 'This Year';
            
            const dropdown = document.getElementById('chartTimeframeDropdown');
            if (dropdown) {
                dropdown.textContent = buttonText;
            }
            
            // Fetch data for the selected timeframe
            fetchChartData(period);
        });
    });
}

// Set up event listeners for map timeframe switching
function setupMapTimeframeListeners() {
    const timeframeElements = document.querySelectorAll('.map-timeframe');
    if (!timeframeElements.length) return;
    
    timeframeElements.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active state in dropdown
            document.querySelectorAll('.map-timeframe').forEach(el => el.classList.remove('active'));
            this.classList.add('active');
            
            // Update dropdown button text
            const timeframe = this.getAttribute('data-timeframe');
            let buttonText = 'Last 30 Days';
            if (timeframe === '7') buttonText = 'Last 7 Days';
            else if (timeframe === '90') buttonText = 'Last 90 Days';
            else if (timeframe === 'all') buttonText = 'All Time';
            
            const dropdown = document.getElementById('mapTimeframeDropdown');
            if (dropdown) {
                dropdown.textContent = buttonText;
            }
            
            // Refresh map data
            refreshMapData(timeframe);
        });
    });
}

// Function to fetch chart data based on time period
function fetchChartData(period) {
    // Show loading state
    const chartError = document.getElementById('chart-error-message');
    const chartContainer = document.querySelector('.chart-container');
    
    if (chartError) chartError.style.display = 'none';
    if (chartContainer) chartContainer.classList.add('loading');
    
    // Add CSRF token to headers for Laravel
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    
    // Fetch data from the API endpoint
    fetch(`/api/vendor/analytics?period=${period}`, {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Update charts with the real data
        updateCharts(data);
        
        // Remove loading state
        if (chartContainer) chartContainer.classList.remove('loading');
    })
    .catch(error => {
        console.error('Error fetching chart data:', error);
        
        // Show error message
        if (chartError) {
            chartError.style.display = 'block';
        }
        
        // Use simulated data as fallback
        updateChartsWithSimulatedData(period);
        
        // Remove loading state
        if (chartContainer) chartContainer.classList.remove('loading');
    });
}

// Function to update all charts with real data
function updateCharts(data) {
    if (data.revenue_data && revenueChart) {
        updateRevenueChart(revenueChart, data.revenue_data);
    }
    
    if (data.category_data && categoryChart) {
        updateCategoryChart(categoryChart, data.category_data);
    }
    
    if (data.customer_data && customerChart) {
        updateCustomerChart(customerChart, data.customer_data);
    }
}

// Function to update charts with simulated data (fallback)
function updateChartsWithSimulatedData(period) {
    console.log('Using simulated data for period:', period);

    // Generate simulated data based on period
    const simulatedData = {
        revenue_data: generateSimulatedRevenueData(period),
        category_data: generateSimulatedCategoryData(),
        customer_data: generateSimulatedCustomerData(period)
    };

    // Update charts with simulated data
    updateCharts(simulatedData);
}

// Chart update functions
function updateRevenueChart(chart, data) {
    if (chart && data) {
        chart.data = data;
        chart.update();
    }
}

function updateCategoryChart(chart, data) {
    if (chart && data) {
        chart.data = data;
        chart.update();
    }
}

function updateCustomerChart(chart, data) {
    if (chart && data) {
        chart.data = data;
        chart.update();
    }
}

// Simulated data generation functions
function generateSimulatedRevenueData(period) {
    const data = {
        labels: [],
        datasets: [{
            label: 'Revenue',
            data: [],
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            borderColor: '#000',
            borderWidth: 2,
            pointBackgroundColor: '#000',
            pointRadius: 4,
            pointHoverRadius: 6
        }, {
            label: 'Orders',
            data: [],
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            borderColor: 'rgba(0, 0, 0, 0)',
            borderWidth: 1,
            type: 'bar',
            yAxisID: 'y1'
        }]
    };

    // Generate labels and data based on period
    if (period === 'week') {
        data.labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        data.datasets[0].data = [1200, 1500, 1800, 2200, 2800, 3200, 2900];
        data.datasets[1].data = [12, 15, 18, 22, 28, 32, 29];
    } else if (period === 'month') {
        for (let i = 1; i <= 30; i++) {
            data.labels.push(i);
            data.datasets[0].data.push(Math.round(1000 + (i * 50) + (Math.random() * 500)));
            data.datasets[1].data.push(Math.round(10 + (i * 0.5) + (Math.random() * 5)));
        }
    } else if (period === 'year') {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        data.labels = months;
        data.datasets[0].data = [15000, 18000, 22000, 25000, 28000, 32000, 35000, 38000, 42000, 45000, 48000, 52000];
        data.datasets[1].data = [150, 180, 220, 250, 280, 320, 350, 380, 420, 450, 480, 520];
    } else { // 6months
        const months = ['Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan'];
        data.labels = months;
        data.datasets[0].data = [28000, 32000, 35000, 38000, 42000, 45000];
        data.datasets[1].data = [280, 320, 350, 380, 420, 450];
    }

    return data;
}

function generateSimulatedCategoryData() {
    return {
        labels: ['Clothing', 'Electronics', 'Home & Garden', 'Beauty', 'Books'],
        datasets: [{
            data: [35, 25, 20, 15, 5],
            backgroundColor: [
                'rgba(0, 0, 0, 0.8)',
                'rgba(0, 0, 0, 0.6)',
                'rgba(0, 0, 0, 0.4)',
                'rgba(0, 0, 0, 0.2)',
                'rgba(0, 0, 0, 0.1)'
            ],
            borderWidth: 1,
            borderColor: '#fff'
        }]
    };
}

function generateSimulatedCustomerData(period) {
    const data = {
        labels: [],
        datasets: [{
            label: 'New Customers',
            data: [],
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
            borderColor: '#000',
            borderWidth: 2,
            pointBackgroundColor: '#000',
            tension: 0.3
        }, {
            label: 'Returning Customers',
            data: [],
            backgroundColor: 'rgba(0, 0, 0, 0.02)',
            borderColor: '#666',
            borderWidth: 2,
            pointBackgroundColor: '#666',
            borderDash: [5, 5],
            tension: 0.3
        }]
    };

    // Generate labels and data based on period
    if (period === 'week') {
        data.labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        data.datasets[0].data = [5, 8, 12, 15, 18, 22, 20];
        data.datasets[1].data = [10, 15, 20, 25, 30, 35, 32];
    } else if (period === 'month') {
        for (let i = 1; i <= 30; i++) {
            data.labels.push(i);
            data.datasets[0].data.push(Math.round(5 + (i * 0.3) + (Math.random() * 3)));
            data.datasets[1].data.push(Math.round(10 + (i * 0.5) + (Math.random() * 5)));
        }
    } else if (period === 'year') {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        data.labels = months;
        data.datasets[0].data = [45, 52, 58, 65, 72, 78, 85, 92, 98, 105, 112, 120];
        data.datasets[1].data = [90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255];
    } else { // 6months
        const months = ['Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan'];
        data.labels = months;
        data.datasets[0].data = [72, 78, 85, 92, 98, 105];
        data.datasets[1].data = [150, 165, 180, 195, 210, 225];
    }

    return data;
}

// Generate simulated map data for fallback
function generateSimulatedMapData(timeframe = 30) {
    const baseData = {
        'Lagos': 42,
        'FCT': 28,
        'Rivers': 19,
        'Kano': 15,
        'Oyo': 12,
        'Enugu': 10,
        'Delta': 8,
        'Kaduna': 7,
        'Edo': 5,
        'Akwa Ibom': 4
    };

    // Adjust based on timeframe
    const multiplier = timeframe === 7 ? 0.3 : (timeframe === 90 ? 2.5 : 1);
    const adjustedData = {};

    for (const [state, count] of Object.entries(baseData)) {
        adjustedData[state] = Math.round(count * multiplier);
    }

    return {
        orders_by_state: adjustedData,
        timeframe: timeframe
    };
}

// Function to refresh map data
function refreshMapData(timeframe = '30') {
    const mapContainer = document.getElementById('nigeria-map-container');
    const loadingOverlay = document.getElementById('map-loading-overlay');
    const errorOverlay = document.getElementById('map-error-message');
    const stateOrdersTable = document.getElementById('state-orders-table');
    
    // Show loading state
    if (loadingOverlay) loadingOverlay.style.display = 'flex';
    if (errorOverlay) errorOverlay.style.display = 'none';
    if (stateOrdersTable) {
        stateOrdersTable.innerHTML = '<tr><td colspan="3" class="text-center py-3"><div class="spinner-border spinner-border-sm" role="status"></div> Loading...</td></tr>';
    }
    
    // Add CSRF token to headers for Laravel
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    
    // Fetch map data from the API endpoint
    fetch(`/api/vendor/orders/by-state?timeframe=${timeframe}`, {
        headers: {
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        // Check if we were redirected to login page
        if (response.url.includes('/login') || response.url.includes('/auth')) {
            throw new Error('Authentication required - redirected to login');
        }

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Response is not JSON - likely authentication issue');
        }

        return response.json();
    })
    .then(data => {
        // Update map with the real data
        if (typeof updateNigeriaMap === 'function') {
            updateNigeriaMap(data);
        }

        // Update state orders table
        updateStateOrdersTable(data);

        // Hide loading state
        if (loadingOverlay) loadingOverlay.style.display = 'none';
    })
    .catch(error => {
        // Hide loading state
        if (loadingOverlay) loadingOverlay.style.display = 'none';

        // Handle different types of errors
        if (error.message.includes('404')) {
            console.info('API endpoint not found, using fallback data');
        } else if (error.message.includes('Authentication required') || error.message.includes('not JSON')) {
            console.warn('Authentication issue detected, using fallback data');
            // Don't show error overlay for auth issues, just use fallback data silently
        } else {
            console.error('API error:', error.message);
            if (errorOverlay) {
                errorOverlay.style.display = 'flex';
            }
        }

        // Use simulated data as fallback
        if (typeof updateNigeriaMap === 'function') {
            updateNigeriaMap(generateSimulatedMapData());
        }

        // Generate fallback state data for the table
        const fallbackStateData = {
            states_details: [
                { state: 'Lagos', orders: 42, value: 125000 },
                { state: 'FCT', orders: 28, value: 89000 },
                { state: 'Rivers', orders: 19, value: 76000 },
                { state: 'Kano', orders: 15, value: 45000 },
                { state: 'Oyo', orders: 12, value: 38000 }
            ]
        };
        updateStateOrdersTable(fallbackStateData);
    });
}

// Function to update state orders table
function updateStateOrdersTable(data) {
    const stateOrdersTable = document.getElementById('state-orders-table');
    if (!stateOrdersTable) return;

    // Clear existing rows
    stateOrdersTable.innerHTML = '';

    // Handle different data formats
    let statesData = [];

    if (data && data.states_details && Array.isArray(data.states_details)) {
        // Format from API response
        statesData = data.states_details;
    } else if (data && typeof data === 'object') {
        // Format from object with state names as keys
        statesData = Object.entries(data).map(([state, stateData]) => ({
            state: state,
            orders: stateData.order_count || stateData.orders || 0,
            value: stateData.order_value || stateData.value || 0
        }));
    }

    // Check if we have data
    if (!statesData || statesData.length === 0) {
        stateOrdersTable.innerHTML = '<tr><td colspan="3" class="text-center py-3">No order data available</td></tr>';
        return;
    }

    // Sort states by order count (descending)
    const sortedStates = statesData.sort((a, b) => (b.orders || 0) - (a.orders || 0));

    // Add rows for each state
    sortedStates.forEach((stateData) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="ps-3">${stateData.state}</td>
            <td>${stateData.orders || 0}</td>
            <td class="pe-3">₦${(stateData.value || 0).toLocaleString('en-NG', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
        `;
        stateOrdersTable.appendChild(row);
    });
}

// Initialize the dashboard when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for vendor dashboard...');
    console.log('vendor-dashboard element:', document.querySelector('.vendor-dashboard'));
    console.log('vendor-dashboard-container element:', document.querySelector('.vendor-dashboard-container'));
    console.log('nigeria-map element:', document.querySelector('#nigeria-map'));

    // Add a visible indicator that JavaScript is working
    const indicator = document.createElement('div');
    indicator.id = 'js-indicator';
    indicator.style.cssText = 'position: fixed; top: 10px; right: 10px; background: green; color: white; padding: 5px; z-index: 9999; font-size: 12px;';
    indicator.textContent = 'JS Loaded';
    document.body.appendChild(indicator);

    // Check if we're on the vendor dashboard
    if (document.querySelector('.vendor-dashboard') || document.querySelector('.vendor-dashboard-container') || document.querySelector('#nigeria-map')) {
        console.log('Vendor dashboard detected, initializing widgets...');
        indicator.textContent = 'JS + Dashboard Detected';
        indicator.style.background = 'blue';
        initDashboardWidgets();
    } else {
        console.log('Not on vendor dashboard page');
        indicator.textContent = 'JS Loaded - Not Dashboard';
        indicator.style.background = 'orange';
    }
});
