<?php

namespace App\Livewire\Auth;

use App\Models\User;
use App\Models\Role;
use App\Models\Vendor;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Str;

#[Layout('components.layouts.auth-bootstrap')]
class VendorRegister extends Component
{
    use WithFileUploads;

    // Personal Information
    public string $name = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';
    public string $phone = '';

    // Business Information
    public string $business_name = '';
    public string $business_address = '';
    public string $business_description = '';
    public string $city = '';
    public string $state = '';
    public string $country = 'Nigeria';

    // File uploads
    public $logo;
    public $id_document;
    public $business_document;

    // Terms acceptance
    public bool $terms = false;

    public $registering = false;

    protected function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            'phone' => ['required', 'string', 'max:20'],
            'business_name' => ['required', 'string', 'max:255', 'unique:vendors,shop_name'],
            'business_address' => ['required', 'string', 'max:500'],
            'business_description' => ['required', 'string', 'max:1000'],
            'city' => ['required', 'string', 'max:100'],
            'state' => ['required', 'string', 'max:100'],
            'country' => ['required', 'string', 'max:100'],
            'logo' => ['nullable', 'image', 'mimes:jpg,jpeg,png,gif', 'max:2048'],
            'id_document' => ['required', 'file', 'mimes:jpg,jpeg,png,pdf', 'max:5120'],
            'business_document' => ['nullable', 'file', 'mimes:jpg,jpeg,png,pdf', 'max:5120'],
            'terms' => ['required', 'accepted'],
        ];
    }

    protected function messages()
    {
        return [
            'business_name.unique' => 'This business name is already taken. Please choose another.',
            'id_document.required' => 'Please upload a valid ID document.',
            'terms.accepted' => 'You must accept the terms and conditions to register.',
        ];
    }

    /**
     * Handle vendor registration request.
     */
    public function register(): void
    {
        $this->registering = true;
        
        try {
            $validated = $this->validate();

            // Get the vendor role
            $vendorRole = Role::where('name', 'vendor')->first();
            if (!$vendorRole) {
                throw new \Exception('Vendor role not found in the system. Please contact administrator.');
            }

            // Create the user first
            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role_id' => $vendorRole->id,
            ]);

            // Process uploaded files
            $logoPath = null;
            $idDocPath = null;
            $businessDocPath = null;

            if ($this->logo) {
                $logoPath = $this->logo->store('public/vendor/logos');
                $logoPath = Storage::url($logoPath);
            }

            if ($this->id_document) {
                $idDocPath = $this->id_document->store('private/vendor/documents');
            }

            if ($this->business_document) {
                $businessDocPath = $this->business_document->store('private/vendor/documents');
            }

            // Create vendor profile
            $vendor = Vendor::create([
                'user_id' => $user->id,
                'shop_name' => $validated['business_name'],
                'slug' => Str::slug($validated['business_name']),
                'description' => $validated['business_description'],
                'address' => $validated['business_address'],
                'city' => $validated['city'],
                'state' => $validated['state'],
                'country' => $validated['country'],
                'logo' => $logoPath,
                'is_approved' => false, // Requires admin approval
                'is_featured' => false,
                'subscription_status' => 'inactive',
                'orders_processed' => 0,
                'free_order_limit' => 10, // Default free orders
                'subscription_required' => false,
                'monthly_subscription_fee' => 5000, // Default fee in kobo
            ]);

            // Store additional data in vendor profile
            $vendor->update([
                'phone' => $validated['phone'],
                'id_document_path' => $idDocPath,
                'business_document_path' => $businessDocPath,
            ]);

            event(new Registered($user));

            Auth::login($user);
            
            session()->flash('success', 'Vendor registration successful! Your account is pending approval.');
            
        } catch (\Exception $e) {
            session()->flash('error', 'Registration failed: ' . $e->getMessage());
            $this->registering = false;
            return;
        }

        $this->registering = false;

        $this->redirect(route('vendor.dashboard', absolute: false), navigate: true);
    }

    public function render()
    {
        return view('livewire.auth.vendor-register');
    }
}
