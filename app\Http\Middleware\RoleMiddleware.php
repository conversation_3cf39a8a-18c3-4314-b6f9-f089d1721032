<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $role
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        if (!auth()->check()) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Unauthenticated'], 401);
            }
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Check if user has the required role
        switch ($role) {
            case 'admin':
                if (!$user->isAdmin()) {
                    return $this->unauthorizedResponse($request, 'Admin access required');
                }
                break;
            
            case 'vendor':
                if (!$user->isVendor()) {
                    return $this->unauthorizedResponse($request, 'Vendor access required');
                }
                
                // Additional vendor checks
                $vendor = $user->vendor;
                if (!$vendor) {
                    if ($request->expectsJson()) {
                        return response()->json(['error' => 'Vendor profile not found'], 404);
                    }
                    return redirect()->route('vendor.register')
                        ->with('error', 'Please complete your vendor registration.');
                }
                
                // Check if vendor is approved
                if (!$vendor->approved) {
                    if ($request->expectsJson()) {
                        return response()->json(['error' => 'Vendor account pending approval'], 403);
                    }
                    return redirect()->route('vendor.pending')
                        ->with('info', 'Your vendor account is pending approval.');
                }
                break;
            
            case 'customer':
                // Regular authenticated user (not admin or vendor)
                if ($user->isAdmin() || $user->isVendor()) {
                    return $this->unauthorizedResponse($request, 'Customer access required');
                }
                break;
            
            default:
                return $this->unauthorizedResponse($request, 'Invalid role specified');
        }

        return $next($request);
    }

    /**
     * Return appropriate unauthorized response
     */
    private function unauthorizedResponse(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return response()->json(['error' => $message], 403);
        }
        
        abort(403, $message);
    }
}
