<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\User;
use App\Models\Vendor;
use App\Models\PlatformIncome;
use App\Models\Withdrawal;
use App\Models\Commission;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        // Date range for analytics (default to current month)
        $period = $request->input('period', 'month');
        $startDate = $this->getStartDate($period);
        $endDate = Carbon::now()->endOfDay();

        // Basic statistics
        $totalSales = Order::where('payment_status', 'paid')->sum('total_amount');
        $totalOrders = Order::count();
        $totalCustomers = User::where('role', 'customer')->count();
        $totalVendors = Vendor::count();
        $activeVendors = Vendor::where('approved', true)->count();
        $pendingVendors = Vendor::where('approved', false)->limit(5)->get();

        // Period-specific statistics
        $periodSales = Order::where('payment_status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_amount');

        $periodOrders = Order::whereBetween('created_at', [$startDate, $endDate])->count();

        // Platform income breakdown
        $incomeData = PlatformIncome::getIncomeSummary($startDate, $endDate);
        $totalPlatformIncome = $incomeData['total']['amount'];

        // Withdrawal statistics
        $pendingWithdrawals = Withdrawal::where('status', 'pending')->count();
        $pendingWithdrawalAmount = Withdrawal::where('status', 'pending')->sum('amount');
        $completedWithdrawals = Withdrawal::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        // Recent activities
        $recentOrders = Order::with(['user', 'vendor'])->latest()->limit(5)->get();
        $recentWithdrawals = Withdrawal::with(['vendor.user'])
            ->where('status', 'pending')
            ->latest()
            ->limit(5)
            ->get();

        return view('admin.dashboard', compact(
            'totalSales', 'totalOrders', 'totalCustomers', 'totalVendors', 'activeVendors',
            'pendingVendors', 'periodSales', 'periodOrders', 'incomeData', 'totalPlatformIncome',
            'pendingWithdrawals', 'pendingWithdrawalAmount', 'completedWithdrawals',
            'recentOrders', 'recentWithdrawals', 'period', 'startDate', 'endDate'
        ));
    }

    /**
     * Get start date based on period
     */
    private function getStartDate(string $period): Carbon
    {
        return match($period) {
            'today' => Carbon::today(),
            'week' => Carbon::now()->startOfWeek(),
            'month' => Carbon::now()->startOfMonth(),
            'quarter' => Carbon::now()->startOfQuarter(),
            'year' => Carbon::now()->startOfYear(),
            'all' => Carbon::create(2020, 1, 1), // Platform start date
            default => Carbon::now()->startOfMonth(),
        };
    }
}
