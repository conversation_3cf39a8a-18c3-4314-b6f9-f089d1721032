<?php

namespace App\Services;

use App\Models\Order;
use App\Models\VendorWallet;
use App\Models\PlatformIncome;
use App\Models\Commission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class OrderStatusService
{
    /**
     * Update order status and handle related business logic
     */
    public function updateOrderStatus(Order $order, string $newStatus, array $metadata = []): bool
    {
        try {
            DB::beginTransaction();

            $oldStatus = $order->status;
            
            // Update order status
            $order->update([
                'status' => $newStatus,
                'shipped_at' => $newStatus === 'shipped' ? now() : $order->shipped_at,
                'delivered_at' => $newStatus === 'delivered' ? now() : $order->delivered_at,
            ]);

            // Handle status-specific logic
            switch ($newStatus) {
                case 'processing':
                    $this->handleProcessingStatus($order);
                    break;
                    
                case 'shipped':
                    $this->handleShippedStatus($order, $metadata);
                    break;
                    
                case 'delivered':
                    $this->handleDeliveredStatus($order);
                    break;
                    
                case 'cancelled':
                    $this->handleCancelledStatus($order);
                    break;
                    
                case 'refunded':
                    $this->handleRefundedStatus($order);
                    break;
            }

            DB::commit();

            Log::info('Order status updated successfully', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);

            return true;

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to update order status', [
                'order_id' => $order->id,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Handle processing status
     */
    protected function handleProcessingStatus(Order $order): void
    {
        // Calculate and record commissions if not already done
        if (!$order->commissions()->exists()) {
            $commissionService = new CommissionService();
            $commissionService->calculateOrderCommission($order);
        }

        // Add pending earnings to vendor wallet
        if ($order->vendor_id) {
            $vendor = $order->vendor;
            $wallet = $vendor->wallet ?? VendorWallet::create(['vendor_id' => $vendor->id]);
            
            // Calculate vendor earnings (order total minus platform commission)
            $commissionRate = config('ecommerce.commission.rate', 0.027);
            $platformCommission = $order->total * $commissionRate;
            $vendorEarnings = $order->total - $platformCommission;
            
            // Add to pending balance
            $wallet->addPendingEarnings(
                $vendorEarnings,
                "Earnings from order #{$order->order_number}",
                $order->id
            );

            // Record platform commission income
            PlatformIncome::recordCommission($order, $platformCommission);
        }
    }

    /**
     * Handle shipped status
     */
    protected function handleShippedStatus(Order $order, array $metadata = []): void
    {
        // Update shipping information if provided
        if (isset($metadata['tracking_number'])) {
            $order->update(['tracking_number' => $metadata['tracking_number']]);
        }

        if (isset($metadata['shipping_carrier'])) {
            $order->update(['shipping_carrier' => $metadata['shipping_carrier']]);
        }

        // TODO: Send shipping notification to customer
        // TODO: Integrate with shipping provider for tracking updates
    }

    /**
     * Handle delivered status - Release vendor earnings
     */
    protected function handleDeliveredStatus(Order $order): void
    {
        if ($order->vendor_id) {
            $vendor = $order->vendor;
            $wallet = $vendor->wallet;
            
            if ($wallet) {
                // Calculate vendor earnings
                $commissionRate = config('ecommerce.commission.rate', 0.027);
                $platformCommission = $order->total * $commissionRate;
                $vendorEarnings = $order->total - $platformCommission;
                
                // Release pending earnings to available balance
                $wallet->releasePendingEarnings(
                    $vendorEarnings,
                    "Earnings released for delivered order #{$order->order_number}",
                    $order->id
                );

                // Update commission status to paid
                Commission::where('order_id', $order->id)
                    ->where('vendor_id', $vendor->id)
                    ->update([
                        'status' => 'paid',
                        'paid_at' => now()
                    ]);
            }
        }

        // TODO: Send delivery confirmation to customer
        // TODO: Request customer review/rating
    }

    /**
     * Handle cancelled status
     */
    protected function handleCancelledStatus(Order $order): void
    {
        // Restore product stock
        foreach ($order->items as $item) {
            $product = $item->product;
            if ($product) {
                $product->increment('stock_quantity', $item->quantity);
            }
        }

        // Cancel pending earnings if order was in processing
        if ($order->vendor_id && in_array($order->getOriginal('status'), ['processing', 'shipped'])) {
            $vendor = $order->vendor;
            $wallet = $vendor->wallet;
            
            if ($wallet) {
                $commissionRate = config('ecommerce.commission.rate', 0.027);
                $vendorEarnings = $order->total * (1 - $commissionRate);
                
                // Remove from pending balance
                $wallet->decrement('pending_balance', $vendorEarnings);
                $wallet->decrement('total_earnings', $vendorEarnings);
                
                // Record cancellation transaction
                $wallet->transactions()->create([
                    'type' => 'cancellation',
                    'amount' => -$vendorEarnings,
                    'status' => 'completed',
                    'description' => "Earnings cancelled for order #{$order->order_number}",
                    'order_id' => $order->id,
                    'balance_after' => $wallet->pending_balance,
                ]);
            }
        }

        // Cancel commissions
        Commission::where('order_id', $order->id)->update(['status' => 'cancelled']);

        // TODO: Process refund if payment was made
        // TODO: Send cancellation notification
    }

    /**
     * Handle refunded status
     */
    protected function handleRefundedStatus(Order $order): void
    {
        // Similar to cancelled but also handle refund processing
        $this->handleCancelledStatus($order);

        // TODO: Process actual refund via payment gateway
        // TODO: Record refund transaction
        // TODO: Send refund confirmation
    }

    /**
     * Automatically update order status based on shipping updates
     */
    public function updateFromShippingProvider(Order $order, string $shippingStatus, array $trackingData = []): bool
    {
        $statusMapping = [
            'picked_up' => 'shipped',
            'in_transit' => 'shipped',
            'out_for_delivery' => 'shipped',
            'delivered' => 'delivered',
            'failed_delivery' => 'shipped', // Keep as shipped, may retry
            'returned' => 'cancelled',
        ];

        $newStatus = $statusMapping[$shippingStatus] ?? null;
        
        if ($newStatus && $newStatus !== $order->status) {
            return $this->updateOrderStatus($order, $newStatus, $trackingData);
        }

        return false;
    }

    /**
     * Get available status transitions for an order
     */
    public function getAvailableTransitions(Order $order): array
    {
        $currentStatus = $order->status;
        
        $transitions = [
            'pending' => ['processing', 'cancelled'],
            'processing' => ['shipped', 'cancelled'],
            'shipped' => ['delivered', 'cancelled'],
            'delivered' => ['refunded'], // Only allow refund after delivery
            'cancelled' => [], // No transitions from cancelled
            'refunded' => [], // No transitions from refunded
        ];

        return $transitions[$currentStatus] ?? [];
    }

    /**
     * Check if status transition is valid
     */
    public function isValidTransition(Order $order, string $newStatus): bool
    {
        $availableTransitions = $this->getAvailableTransitions($order);
        return in_array($newStatus, $availableTransitions);
    }

    /**
     * Bulk update order statuses
     */
    public function bulkUpdateStatus(array $orderIds, string $newStatus): array
    {
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];
        
        foreach ($orderIds as $orderId) {
            try {
                $order = Order::findOrFail($orderId);
                
                if ($this->isValidTransition($order, $newStatus)) {
                    if ($this->updateOrderStatus($order, $newStatus)) {
                        $results['success']++;
                    } else {
                        $results['failed']++;
                        $results['errors'][] = "Failed to update order #{$order->order_number}";
                    }
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Invalid status transition for order #{$order->order_number}";
                }
            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Order ID {$orderId}: " . $e->getMessage();
            }
        }
        
        return $results;
    }
}
