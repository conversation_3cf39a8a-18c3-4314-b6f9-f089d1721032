<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Wishlist;
use App\Models\WishlistItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WishlistController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the user's wishlist
     */
    public function index()
    {
        $user = Auth::user();

        // Get or create user's wishlist
        $wishlist = Wishlist::firstOrCreate(['user_id' => $user->id]);

        // Get wishlist items with products
        $wishlistItems = WishlistItem::where('wishlist_id', $wishlist->id)
            ->with(['product.vendor', 'product.category'])
            ->latest()
            ->paginate(12);

        return view('wishlist.index', compact('wishlistItems', 'wishlist'));
    }

    /**
     * Add product to wishlist
     */
    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $user = Auth::user();
        $productId = $request->product_id;

        // Get or create user's wishlist
        $wishlist = Wishlist::firstOrCreate(['user_id' => $user->id]);

        // Check if product is already in wishlist
        $existingItem = WishlistItem::where('wishlist_id', $wishlist->id)
            ->where('product_id', $productId)
            ->first();

        if ($existingItem) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product is already in your wishlist',
                    'in_wishlist' => true
                ]);
            }

            return redirect()->back()->with('info', 'Product is already in your wishlist.');
        }

        // Add product to wishlist
        WishlistItem::create([
            'wishlist_id' => $wishlist->id,
            'product_id' => $productId,
            'quantity' => 1
        ]);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Product added to wishlist',
                'in_wishlist' => true,
                'wishlist_count' => $this->getWishlistCount()
            ]);
        }

        return redirect()->back()->with('success', 'Product added to your wishlist!');
    }

    /**
     * Remove product from wishlist
     */
    public function remove(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $user = Auth::user();
        $productId = $request->product_id;

        // Get user's wishlist
        $wishlist = Wishlist::where('user_id', $user->id)->first();

        if (!$wishlist) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Wishlist not found',
                    'in_wishlist' => false
                ]);
            }

            return redirect()->back()->with('error', 'Wishlist not found.');
        }

        // Remove product from wishlist
        $deleted = WishlistItem::where('wishlist_id', $wishlist->id)
            ->where('product_id', $productId)
            ->delete();

        if ($deleted) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Product removed from wishlist',
                    'in_wishlist' => false,
                    'wishlist_count' => $this->getWishlistCount()
                ]);
            }

            return redirect()->back()->with('success', 'Product removed from your wishlist!');
        }

        if ($request->ajax()) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found in wishlist',
                'in_wishlist' => false
            ]);
        }

        return redirect()->back()->with('error', 'Product not found in your wishlist.');
    }

    /**
     * Toggle product in wishlist
     */
    public function toggle(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $user = Auth::user();
        $productId = $request->product_id;

        // Get or create user's wishlist
        $wishlist = Wishlist::firstOrCreate(['user_id' => $user->id]);

        // Check if product is in wishlist
        $existingItem = WishlistItem::where('wishlist_id', $wishlist->id)
            ->where('product_id', $productId)
            ->first();

        if ($existingItem) {
            // Remove from wishlist
            $existingItem->delete();
            $action = 'removed';
            $inWishlist = false;
            $message = 'Product removed from wishlist';
        } else {
            // Add to wishlist
            WishlistItem::create([
                'wishlist_id' => $wishlist->id,
                'product_id' => $productId,
                'quantity' => 1
            ]);
            $action = 'added';
            $inWishlist = true;
            $message = 'Product added to wishlist';
        }

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'action' => $action,
                'message' => $message,
                'in_wishlist' => $inWishlist,
                'wishlist_count' => $this->getWishlistCount()
            ]);
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Clear entire wishlist
     */
    public function clear()
    {
        $user = Auth::user();
        $wishlist = Wishlist::where('user_id', $user->id)->first();

        if ($wishlist) {
            WishlistItem::where('wishlist_id', $wishlist->id)->delete();
        }

        return redirect()->route('wishlist.index')->with('success', 'Wishlist cleared successfully!');
    }

    /**
     * Get wishlist count for current user
     */
    protected function getWishlistCount(): int
    {
        $user = Auth::user();
        $wishlist = Wishlist::where('user_id', $user->id)->first();

        if (!$wishlist) {
            return 0;
        }

        return WishlistItem::where('wishlist_id', $wishlist->id)->count();
    }
}
