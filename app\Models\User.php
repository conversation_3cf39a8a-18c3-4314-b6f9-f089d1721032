<?php
namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'role_id',
        'email_verified_at',
        'phone',
        'date_of_birth',
        'gender',
        'bio',
        'avatar',
        'email_notifications',
        'order_updates',
        'promotional_emails',
        'security_alerts',
        'profile_visibility',
        'show_email',
        'show_phone',
        'allow_messages',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'date_of_birth' => 'date',
        'email_notifications' => 'boolean',
        'order_updates' => 'boolean',
        'promotional_emails' => 'boolean',
        'security_alerts' => 'boolean',
        'show_email' => 'boolean',
        'show_phone' => 'boolean',
        'allow_messages' => 'boolean',
    ];

    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isVendor()
    {
        return $this->role === 'vendor';
    }
    
    public function vendor()
    {
        return $this->hasOne(Vendor::class);
    }
    
    public function isApprovedVendor()
    {
        return $this->isVendor() && $this->vendor && $this->vendor->approved;
    }
    
    public function wishlist()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function wishlistItems()
    {
        return $this->hasManyThrough(WishlistItem::class, Wishlist::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }
    
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    
    /**
     * Get user's initials.
     *
     * @return string
     */
    public function initials()
    {
        $name = $this->name;
        $words = explode(' ', $name);
        $initials = '';

        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
            }
        }

        return $initials ?: strtoupper(substr($name, 0, 1));
    }

    /**
     * Get user's avatar URL
     *
     * @return string
     */
    public function getAvatarUrlAttribute()
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        // Generate a default avatar using initials
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7F9CF5&background=EBF4FF&size=200';
    }

    /**
     * Get user's full name with title
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return $this->name;
    }

    /**
     * Get user's age
     *
     * @return int|null
     */
    public function getAgeAttribute()
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }
}

