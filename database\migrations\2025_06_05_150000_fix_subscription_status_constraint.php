<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Skip this migration - SQLite compatibility handled by 2025_06_05_190000_fix_sqlite_subscription_status.php
        // This migration used MySQL-specific syntax that doesn't work with SQLite
        echo "Skipping MySQL-specific subscription_status fix - handled by SQLite-compatible migration.\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Skip this migration - handled by SQLite-compatible migration
        echo "Skipping MySQL-specific subscription_status revert - handled by SQLite-compatible migration.\n";
    }
};
