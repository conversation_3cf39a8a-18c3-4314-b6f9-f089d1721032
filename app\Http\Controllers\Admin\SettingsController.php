<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SettingsController extends Controller
{
    public function index()
    {
        $settings = [
            'site_name' => config('app.name'),
            'contact_email' => config('mail.from.address'),
            'support_phone' => config('settings.support_phone', ''),
        ];
        return view('admin.settings.index', compact('settings'));
    }

    public function update(Request $request)
    {
        $data = $request->validate([
            'site_name' => 'required',
            'contact_email' => 'required|email',
            'support_phone' => 'nullable',
        ]);
        
        // Save settings to database or file
        $this->saveSettings($data);
        
        return redirect()->route('admin.settings.index')->with('success', 'Settings updated successfully.');
    }
    
    /**
     * Save settings to the cache system (safer than modifying .env)
     *
     * @param array $data
     * @return void
     */
    private function saveSettings(array $data)
    {
        // Store all settings in cache for immediate use
        foreach ($data as $key => $value) {
            cache()->forever("settings.{$key}", $value);
        }

        // Log the settings update for admin tracking
        Log::info('Admin settings updated', [
            'settings' => array_keys($data),
            'timestamp' => now()
        ]);
    }
}
