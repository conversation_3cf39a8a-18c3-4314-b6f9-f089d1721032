<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class PaystackService
{
    protected $secretKey;
    protected $publicKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->secretKey = config('services.paystack.secret_key');
        $this->publicKey = config('services.paystack.public_key');
        $this->baseUrl = config('services.paystack.payment_url', 'https://api.paystack.co');
    }

    /**
     * Initialize a payment transaction
     */
    public function initializePayment(array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/transaction/initialize', $data);

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Payment initialization failed: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Paystack payment initialization failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Verify a payment transaction
     */
    public function verifyPayment(string $reference): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
            ])->get($this->baseUrl . '/transaction/verify/' . $reference);

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Payment verification failed: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Paystack payment verification failed', [
                'error' => $e->getMessage(),
                'reference' => $reference
            ]);
            throw $e;
        }
    }

    /**
     * Create a subscription plan
     */
    public function createPlan(array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/plan', $data);

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Plan creation failed: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Paystack plan creation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Create a subscription
     */
    public function createSubscription(array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/subscription', $data);

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Subscription creation failed: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Paystack subscription creation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Cancel a subscription
     */
    public function cancelSubscription(string $code, string $token): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/subscription/disable', [
                'code' => $code,
                'token' => $token
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Subscription cancellation failed: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Paystack subscription cancellation failed', [
                'error' => $e->getMessage(),
                'code' => $code
            ]);
            throw $e;
        }
    }

    /**
     * Create a transfer recipient
     */
    public function createTransferRecipient(array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/transferrecipient', $data);

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Transfer recipient creation failed: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Paystack transfer recipient creation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Initiate a transfer
     */
    public function initiateTransfer(array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/transfer', $data);

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Transfer initiation failed: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Paystack transfer initiation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Verify a transfer
     */
    public function verifyTransfer(string $reference): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
            ])->get($this->baseUrl . '/transfer/verify/' . $reference);

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Transfer verification failed: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Paystack transfer verification failed', [
                'error' => $e->getMessage(),
                'reference' => $reference
            ]);
            throw $e;
        }
    }

    /**
     * Verify account number
     */
    public function verifyAccountNumber(string $accountNumber, string $bankCode): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
            ])->get($this->baseUrl . '/bank/resolve', [
                'account_number' => $accountNumber,
                'bank_code' => $bankCode
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Account verification failed: ' . $response->body());
        } catch (Exception $e) {
            Log::error('Paystack account verification failed', [
                'error' => $e->getMessage(),
                'account_number' => $accountNumber,
                'bank_code' => $bankCode
            ]);
            throw $e;
        }
    }

    /**
     * Get list of banks
     */
    public function getBanks(): array
    {
        try {
            // Cache banks list for 24 hours
            return Cache::remember('paystack_banks', 86400, function () {
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $this->secretKey,
                ])->get($this->baseUrl . '/bank');

                if ($response->successful()) {
                    return $response->json();
                }

                throw new Exception('Failed to fetch banks: ' . $response->body());
            });
        } catch (Exception $e) {
            Log::error('Paystack banks fetch failed', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        $computedSignature = hash_hmac('sha512', $payload, $this->secretKey);
        return hash_equals($signature, $computedSignature);
    }

    /**
     * Convert amount to kobo (Paystack's smallest currency unit)
     */
    public function convertToKobo(float $amount): int
    {
        return (int) ($amount * 100);
    }

    /**
     * Convert amount from kobo to naira
     */
    public function convertFromKobo(int $amount): float
    {
        return $amount / 100;
    }
}
