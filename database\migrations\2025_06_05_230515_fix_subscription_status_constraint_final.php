<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the table to remove the CHECK constraint
        // and allow 'cancelled' as a valid subscription status

        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // Get all data from vendors table
            $vendors = DB::table('vendors')->get();

            // Drop and recreate the table without the problematic constraint
            Schema::dropIfExists('vendors_backup');

            // Create backup table first
            Schema::create('vendors_backup', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('shop_name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->text('brand_description')->nullable();
                $table->string('address')->nullable();
                $table->string('city')->nullable();
                $table->string('state')->nullable();
                $table->string('country')->nullable();
                $table->string('logo')->nullable();
                $table->string('brand_logo')->nullable();
                $table->boolean('approved')->default(false);
                $table->boolean('is_featured')->default(false);
                $table->string('business_name')->nullable();
                $table->string('address_line1')->nullable();
                $table->string('contact_phone')->nullable();
                $table->string('contact_email_business')->nullable();
                $table->enum('delivery_zone_type', ['platform_delivery_zone', 'other_states_zone'])->nullable();
                $table->string('subscription_status', 50)->default('pending_payment'); // String instead of enum
                $table->integer('orders_processed')->default(0);
                $table->integer('free_order_limit')->default(10);
                $table->timestamp('subscription_started_at')->nullable();
                $table->timestamp('subscription_expires_at')->nullable();
                $table->decimal('monthly_subscription_fee', 8, 2)->default(50.00);
                $table->boolean('subscription_required')->default(false);
                $table->timestamp('cancelled_at')->nullable();
                $table->timestamps();
            });

            // Copy data to backup
            foreach ($vendors as $vendor) {
                DB::table('vendors_backup')->insert((array) $vendor);
            }

            // Drop original table
            Schema::dropIfExists('vendors');

            // Recreate vendors table with proper structure
            Schema::create('vendors', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('shop_name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->text('brand_description')->nullable();
                $table->string('address')->nullable();
                $table->string('city')->nullable();
                $table->string('state')->nullable();
                $table->string('country')->nullable();
                $table->string('logo')->nullable();
                $table->string('brand_logo')->nullable();
                $table->boolean('approved')->default(false);
                $table->boolean('is_featured')->default(false);
                $table->string('business_name')->nullable();
                $table->string('address_line1')->nullable();
                $table->string('contact_phone')->nullable();
                $table->string('contact_email_business')->nullable();
                $table->enum('delivery_zone_type', ['platform_delivery_zone', 'other_states_zone'])->nullable();
                $table->string('subscription_status', 50)->default('pending_payment'); // String field - no constraints
                $table->integer('orders_processed')->default(0);
                $table->integer('free_order_limit')->default(10);
                $table->timestamp('subscription_started_at')->nullable();
                $table->timestamp('subscription_expires_at')->nullable();
                $table->decimal('monthly_subscription_fee', 8, 2)->default(50.00);
                $table->boolean('subscription_required')->default(false);
                $table->timestamp('cancelled_at')->nullable();
                $table->timestamps();
            });

            // Copy data back
            foreach ($vendors as $vendor) {
                DB::table('vendors')->insert((array) $vendor);
            }

            // Drop backup table
            Schema::dropIfExists('vendors_backup');

            echo "SQLite vendors table recreated with flexible subscription_status field\n";
        } else {
            // For MySQL/PostgreSQL, modify the enum to include 'cancelled'
            DB::statement("ALTER TABLE vendors MODIFY COLUMN subscription_status ENUM('active', 'inactive', 'pending_payment', 'expired', 'cancelled') DEFAULT 'pending_payment'");
            echo "MySQL/PostgreSQL subscription_status enum updated to include 'cancelled'\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This is a destructive operation, so we'll leave it as is
        echo "Down migration skipped - this would be destructive\n";
    }
};
