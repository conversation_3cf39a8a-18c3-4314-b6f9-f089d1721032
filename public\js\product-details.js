/**
 * Product Details Page JavaScript Enhancements
 * Handles image gallery, size/color selection, quantity updates, and other interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeProductDetails();
});

function initializeProductDetails() {
    // Initialize all product detail features
    initializeImageGallery();
    initializeQuantityControls();
    initializeVariantSelection();
    initializeReviewSystem();
    initializeWishlistToggle();
    initializeShareButtons();
    initializeZoomFeature();
}

/**
 * Image Gallery with Thumbnails
 */
function initializeImageGallery() {
    const mainImage = document.getElementById('main-image');
    const thumbnails = document.querySelectorAll('.thumbnail');
    
    if (!mainImage || thumbnails.length === 0) return;

    thumbnails.forEach((thumbnail, index) => {
        thumbnail.addEventListener('click', function() {
            // Update main image
            mainImage.src = this.src;
            mainImage.alt = this.alt;

            // Update active thumbnail
            thumbnails.forEach(thumb => thumb.classList.remove('active'));
            this.classList.add('active');

            // Add smooth transition effect
            mainImage.style.opacity = '0';
            setTimeout(() => {
                mainImage.style.opacity = '1';
            }, 150);
        });

        // Add keyboard navigation
        thumbnail.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });

    // Add arrow key navigation for gallery
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
            const activeThumbnail = document.querySelector('.thumbnail.active');
            if (!activeThumbnail) return;

            const currentIndex = Array.from(thumbnails).indexOf(activeThumbnail);
            let nextIndex;

            if (e.key === 'ArrowLeft') {
                nextIndex = currentIndex > 0 ? currentIndex - 1 : thumbnails.length - 1;
            } else {
                nextIndex = currentIndex < thumbnails.length - 1 ? currentIndex + 1 : 0;
            }

            thumbnails[nextIndex].click();
        }
    });
}

/**
 * Enhanced Quantity Controls
 */
function initializeQuantityControls() {
    const decreaseBtn = document.getElementById('decrease-quantity');
    const increaseBtn = document.getElementById('increase-quantity');
    const quantityInput = document.getElementById('quantity');
    
    if (!decreaseBtn || !increaseBtn || !quantityInput) return;

    const maxQuantity = parseInt(quantityInput.getAttribute('max')) || 999;
    const minQuantity = parseInt(quantityInput.getAttribute('min')) || 1;

    // Decrease quantity
    decreaseBtn.addEventListener('click', function() {
        let currentValue = parseInt(quantityInput.value);
        if (currentValue > minQuantity) {
            quantityInput.value = currentValue - 1;
            updateQuantityDisplay();
        }
    });

    // Increase quantity
    increaseBtn.addEventListener('click', function() {
        let currentValue = parseInt(quantityInput.value);
        if (currentValue < maxQuantity) {
            quantityInput.value = currentValue + 1;
            updateQuantityDisplay();
        }
    });

    // Handle direct input
    quantityInput.addEventListener('input', function() {
        let value = parseInt(this.value);
        if (isNaN(value) || value < minQuantity) {
            this.value = minQuantity;
        } else if (value > maxQuantity) {
            this.value = maxQuantity;
        }
        updateQuantityDisplay();
    });

    function updateQuantityDisplay() {
        const quantity = parseInt(quantityInput.value);
        
        // Update button states
        decreaseBtn.disabled = quantity <= minQuantity;
        increaseBtn.disabled = quantity >= maxQuantity;
        
        // Update any price calculations if needed
        updatePriceDisplay(quantity);
    }

    // Initialize display
    updateQuantityDisplay();
}

/**
 * Variant Selection (Size, Color, etc.)
 */
function initializeVariantSelection() {
    const variantSelectors = document.querySelectorAll('.variant-selector');
    
    variantSelectors.forEach(selector => {
        const options = selector.querySelectorAll('.variant-option');
        
        options.forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from siblings
                options.forEach(opt => opt.classList.remove('active'));
                
                // Add active class to clicked option
                this.classList.add('active');
                
                // Update hidden input if exists
                const hiddenInput = selector.querySelector('input[type="hidden"]');
                if (hiddenInput) {
                    hiddenInput.value = this.dataset.value;
                }
                
                // Trigger variant change event
                const event = new CustomEvent('variantChanged', {
                    detail: {
                        type: selector.dataset.type,
                        value: this.dataset.value,
                        element: this
                    }
                });
                document.dispatchEvent(event);
            });
        });
    });
}

/**
 * Review System Enhancements
 */
function initializeReviewSystem() {
    const ratingStars = document.querySelectorAll('.rating-input .star');
    const ratingInput = document.getElementById('rating-input');
    let selectedRating = 0;

    ratingStars.forEach((star, index) => {
        const rating = index + 1;
        
        star.addEventListener('mouseover', function() {
            highlightStars(rating);
        });

        star.addEventListener('mouseout', function() {
            highlightStars(selectedRating);
        });

        star.addEventListener('click', function() {
            selectedRating = rating;
            if (ratingInput) {
                ratingInput.value = selectedRating;
            }
            highlightStars(selectedRating);
        });
    });

    function highlightStars(rating) {
        ratingStars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('far');
                star.classList.add('fas');
            } else {
                star.classList.remove('fas');
                star.classList.add('far');
            }
        });
    }

    // Review form validation
    const reviewForm = document.getElementById('review-form');
    if (reviewForm) {
        reviewForm.addEventListener('submit', function(e) {
            if (selectedRating === 0) {
                e.preventDefault();
                showNotification('Please select a rating before submitting your review.', 'warning');
                return false;
            }
        });
    }
}

/**
 * Wishlist Toggle
 */
function initializeWishlistToggle() {
    const wishlistBtns = document.querySelectorAll('.wishlist-toggle');
    
    wishlistBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            
            const productId = this.dataset.productId;
            const isInWishlist = this.classList.contains('in-wishlist');
            
            // Toggle wishlist via AJAX
            toggleWishlist(productId, isInWishlist, this);
        });
    });
}

/**
 * Share Buttons
 */
function initializeShareButtons() {
    const shareButtons = document.querySelectorAll('.share-btn');
    
    shareButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            
            const platform = this.dataset.platform;
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent(document.title);
            
            let shareUrl = '';
            
            switch(platform) {
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
                    break;
                case 'whatsapp':
                    shareUrl = `https://wa.me/?text=${title} ${url}`;
                    break;
                case 'copy':
                    copyToClipboard(window.location.href);
                    return;
            }
            
            if (shareUrl) {
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
        });
    });
}

/**
 * Image Zoom Feature
 */
function initializeZoomFeature() {
    const mainImage = document.getElementById('main-image');
    if (!mainImage) return;

    let isZoomed = false;
    
    mainImage.addEventListener('click', function() {
        if (!isZoomed) {
            this.style.transform = 'scale(2)';
            this.style.cursor = 'zoom-out';
            this.style.transition = 'transform 0.3s ease';
            isZoomed = true;
        } else {
            this.style.transform = 'scale(1)';
            this.style.cursor = 'zoom-in';
            isZoomed = false;
        }
    });
}

/**
 * Helper Functions
 */
function updatePriceDisplay(quantity) {
    const priceElement = document.querySelector('.product-price');
    const basePriceElement = document.querySelector('[data-base-price]');
    
    if (priceElement && basePriceElement) {
        const basePrice = parseFloat(basePriceElement.dataset.basePrice);
        const totalPrice = basePrice * quantity;
        priceElement.textContent = `₦${totalPrice.toLocaleString()}`;
    }
}

function toggleWishlist(productId, isInWishlist, button) {
    const url = isInWishlist ? '/wishlist/remove' : '/wishlist/add';
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        },
        body: JSON.stringify({ product_id: productId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.classList.toggle('in-wishlist');
            const icon = button.querySelector('i');
            if (icon) {
                icon.classList.toggle('far');
                icon.classList.toggle('fas');
            }
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message || 'An error occurred', 'error');
        }
    })
    .catch(error => {
        console.error('Wishlist error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Link copied to clipboard!', 'success');
    }).catch(() => {
        showNotification('Failed to copy link', 'error');
    });
}

function showNotification(message, type = 'info') {
    // Use the same notification system as cart.js
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} notification-toast`;
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            <span>${message}</span>
        </div>
    `;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '250px';
    notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    notification.style.transition = 'all 0.3s ease';
    notification.style.opacity = '0';
    notification.style.transform = 'translateY(-20px)';
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
    }, 10);
    
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
