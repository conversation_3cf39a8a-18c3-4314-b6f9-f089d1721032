<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendorWallet extends Model
{
    use HasFactory;

    protected $fillable = [
        'vendor_id',
        'available_balance',
        'pending_balance',
        'total_earnings',
        'total_withdrawn',
        'last_updated_at',
    ];

    protected $casts = [
        'available_balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'total_earnings' => 'decimal:2',
        'total_withdrawn' => 'decimal:2',
        'last_updated_at' => 'datetime',
    ];

    /**
     * Get the vendor that owns the wallet
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get all wallet transactions
     */
    public function transactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Add earnings to the wallet (pending until order is delivered)
     */
    public function addPendingEarnings(float $amount, string $description, $orderId = null): WalletTransaction
    {
        $this->increment('pending_balance', $amount);
        $this->increment('total_earnings', $amount);
        $this->touch('last_updated_at');

        return $this->transactions()->create([
            'type' => 'earning',
            'amount' => $amount,
            'status' => 'pending',
            'description' => $description,
            'order_id' => $orderId,
            'balance_after' => $this->pending_balance,
        ]);
    }

    /**
     * Release pending earnings to available balance (when order is delivered)
     */
    public function releasePendingEarnings(float $amount, string $description, $orderId = null): WalletTransaction
    {
        if ($this->pending_balance < $amount) {
            throw new \Exception('Insufficient pending balance');
        }

        $this->decrement('pending_balance', $amount);
        $this->increment('available_balance', $amount);
        $this->touch('last_updated_at');

        return $this->transactions()->create([
            'type' => 'release',
            'amount' => $amount,
            'status' => 'completed',
            'description' => $description,
            'order_id' => $orderId,
            'balance_after' => $this->available_balance,
        ]);
    }

    /**
     * Deduct amount for withdrawal
     */
    public function deductForWithdrawal(float $amount, string $description, $withdrawalId = null): WalletTransaction
    {
        if ($this->available_balance < $amount) {
            throw new \Exception('Insufficient available balance');
        }

        $this->decrement('available_balance', $amount);
        $this->increment('total_withdrawn', $amount);
        $this->touch('last_updated_at');

        return $this->transactions()->create([
            'type' => 'withdrawal',
            'amount' => -$amount, // Negative for withdrawal
            'status' => 'completed',
            'description' => $description,
            'withdrawal_id' => $withdrawalId,
            'balance_after' => $this->available_balance,
        ]);
    }

    /**
     * Add platform fee deduction
     */
    public function deductPlatformFee(float $amount, string $description, $orderId = null): WalletTransaction
    {
        $this->decrement('available_balance', $amount);
        $this->touch('last_updated_at');

        return $this->transactions()->create([
            'type' => 'fee',
            'amount' => -$amount, // Negative for fee
            'status' => 'completed',
            'description' => $description,
            'order_id' => $orderId,
            'balance_after' => $this->available_balance,
        ]);
    }

    /**
     * Get total balance (available + pending)
     */
    public function getTotalBalanceAttribute(): float
    {
        return $this->available_balance + $this->pending_balance;
    }

    /**
     * Check if vendor can withdraw a specific amount
     */
    public function canWithdraw(float $amount): bool
    {
        return $this->available_balance >= $amount;
    }

    /**
     * Get recent transactions
     */
    public function getRecentTransactions(int $limit = 10)
    {
        return $this->transactions()
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get transactions by type
     */
    public function getTransactionsByType(string $type, int $limit = null)
    {
        $query = $this->transactions()->where('type', $type)->latest();
        
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }

    /**
     * Get monthly earnings summary
     */
    public function getMonthlyEarnings(int $year, int $month): array
    {
        $startDate = \Carbon\Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = $startDate->copy()->endOfMonth();

        $earnings = $this->transactions()
            ->where('type', 'earning')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        $withdrawals = $this->transactions()
            ->where('type', 'withdrawal')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        $fees = $this->transactions()
            ->where('type', 'fee')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        return [
            'earnings' => $earnings,
            'withdrawals' => abs($withdrawals),
            'fees' => abs($fees),
            'net_earnings' => $earnings + $withdrawals + $fees, // withdrawals and fees are negative
        ];
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Initialize wallet with zero balances when created
        static::creating(function ($wallet) {
            $wallet->available_balance = $wallet->available_balance ?? 0;
            $wallet->pending_balance = $wallet->pending_balance ?? 0;
            $wallet->total_earnings = $wallet->total_earnings ?? 0;
            $wallet->total_withdrawn = $wallet->total_withdrawn ?? 0;
            $wallet->last_updated_at = now();
        });
    }
}
