<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vendor_wallet_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['earning', 'withdrawal', 'fee', 'release', 'refund'])->comment('Transaction type');
            $table->decimal('amount', 15, 2)->comment('Transaction amount (positive for credit, negative for debit)');
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->string('description')->comment('Transaction description');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('withdrawal_id')->nullable()->constrained()->onDelete('set null');
            $table->decimal('balance_after', 15, 2)->comment('Wallet balance after this transaction');
            $table->json('metadata')->nullable()->comment('Additional transaction data');
            $table->timestamps();

            // Indexes
            $table->index(['vendor_wallet_id', 'type']);
            $table->index(['vendor_wallet_id', 'status']);
            $table->index(['vendor_wallet_id', 'created_at']);
            $table->index(['order_id']);
            $table->index(['withdrawal_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_transactions');
    }
};
