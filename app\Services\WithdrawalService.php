<?php

namespace App\Services;

use App\Models\Vendor;
use App\Models\Withdrawal;
use App\Models\VendorWallet;
use App\Services\PaystackService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Exception;

class WithdrawalService
{
    protected $paystackService;

    public function __construct(PaystackService $paystackService)
    {
        $this->paystackService = $paystackService;
    }

    /**
     * Create a withdrawal request
     */
    public function createWithdrawalRequest(Vendor $vendor, array $data): Withdrawal
    {
        try {
            DB::beginTransaction();

            // Get or create vendor wallet
            $wallet = $vendor->wallet ?? $this->createVendorWallet($vendor);

            // Validate withdrawal amount
            if (!$wallet->canWithdraw($data['amount'])) {
                throw new Exception('Insufficient available balance for withdrawal');
            }

            // Validate minimum withdrawal amount
            $minWithdrawal = config('ecommerce.withdrawal.minimum_amount', 1000);
            if ($data['amount'] < $minWithdrawal) {
                throw new Exception("Minimum withdrawal amount is ₦{$minWithdrawal}");
            }

            // Create withdrawal record
            $withdrawal = Withdrawal::create([
                'vendor_id' => $vendor->id,
                'amount' => $data['amount'],
                'method' => $data['method'],
                'details' => $this->prepareWithdrawalDetails($data),
                'status' => 'pending',
                'reference_number' => $this->generateWithdrawalReference(),
                'notes' => $data['notes'] ?? null,
            ]);

            // Reserve the amount in wallet (move from available to pending withdrawal)
            $wallet->deductForWithdrawal(
                $data['amount'],
                "Withdrawal request #{$withdrawal->reference_number}",
                $withdrawal->id
            );

            DB::commit();

            Log::info('Withdrawal request created', [
                'vendor_id' => $vendor->id,
                'withdrawal_id' => $withdrawal->id,
                'amount' => $data['amount']
            ]);

            return $withdrawal;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Withdrawal request creation failed', [
                'vendor_id' => $vendor->id,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Process withdrawal via Paystack
     */
    public function processWithdrawal(Withdrawal $withdrawal): bool
    {
        try {
            DB::beginTransaction();

            if ($withdrawal->status !== 'approved') {
                throw new Exception('Withdrawal must be approved before processing');
            }

            // Create transfer recipient if not exists
            $recipientCode = $this->createOrGetTransferRecipient($withdrawal);

            // Initiate transfer
            $transferData = [
                'source' => 'balance',
                'amount' => $this->paystackService->convertToKobo($withdrawal->amount),
                'recipient' => $recipientCode,
                'reason' => "Withdrawal for vendor #{$withdrawal->vendor_id}",
                'reference' => $withdrawal->reference_number,
            ];

            $transferResponse = $this->paystackService->initiateTransfer($transferData);

            if (!$transferResponse['status']) {
                throw new Exception('Transfer initiation failed: ' . $transferResponse['message']);
            }

            // Update withdrawal with transfer details
            $withdrawal->update([
                'status' => 'processing',
                'paystack_transfer_code' => $transferResponse['data']['transfer_code'],
                'paystack_reference' => $transferResponse['data']['reference'],
                'processed_at' => now(),
            ]);

            DB::commit();

            Log::info('Withdrawal processing initiated', [
                'withdrawal_id' => $withdrawal->id,
                'transfer_code' => $transferResponse['data']['transfer_code']
            ]);

            return true;

        } catch (Exception $e) {
            DB::rollBack();
            
            // Mark withdrawal as failed
            $withdrawal->update([
                'status' => 'failed',
                'notes' => 'Processing failed: ' . $e->getMessage()
            ]);

            Log::error('Withdrawal processing failed', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Handle withdrawal completion (called from webhook)
     */
    public function completeWithdrawal(string $reference): bool
    {
        try {
            $withdrawal = Withdrawal::where('paystack_reference', $reference)->first();
            
            if (!$withdrawal) {
                Log::warning('Withdrawal not found for reference', ['reference' => $reference]);
                return false;
            }

            $withdrawal->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            Log::info('Withdrawal completed', [
                'withdrawal_id' => $withdrawal->id,
                'reference' => $reference
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Withdrawal completion failed', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Handle withdrawal failure (called from webhook)
     */
    public function failWithdrawal(string $reference, string $reason = null): bool
    {
        try {
            DB::beginTransaction();

            $withdrawal = Withdrawal::where('paystack_reference', $reference)->first();
            
            if (!$withdrawal) {
                Log::warning('Withdrawal not found for reference', ['reference' => $reference]);
                return false;
            }

            // Refund the amount back to vendor wallet
            $wallet = $withdrawal->vendor->wallet;
            if ($wallet) {
                $wallet->increment('available_balance', $withdrawal->amount);
                $wallet->transactions()->create([
                    'type' => 'refund',
                    'amount' => $withdrawal->amount,
                    'status' => 'completed',
                    'description' => "Refund for failed withdrawal #{$withdrawal->reference_number}",
                    'withdrawal_id' => $withdrawal->id,
                    'balance_after' => $wallet->available_balance,
                ]);
            }

            $withdrawal->update([
                'status' => 'failed',
                'notes' => $reason ? "Failed: {$reason}" : 'Transfer failed',
            ]);

            DB::commit();

            Log::info('Withdrawal failed and refunded', [
                'withdrawal_id' => $withdrawal->id,
                'reference' => $reference,
                'reason' => $reason
            ]);

            return true;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Withdrawal failure handling failed', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Create or get Paystack transfer recipient
     */
    protected function createOrGetTransferRecipient(Withdrawal $withdrawal): string
    {
        $details = $withdrawal->details;
        
        // Check if recipient already exists
        if (isset($details['paystack_recipient_code'])) {
            return $details['paystack_recipient_code'];
        }

        // Create new recipient
        $recipientData = [
            'type' => 'nuban',
            'name' => $details['account_name'],
            'account_number' => $details['account_number'],
            'bank_code' => $details['bank_code'],
            'currency' => 'NGN',
        ];

        $response = $this->paystackService->createTransferRecipient($recipientData);

        if (!$response['status']) {
            throw new Exception('Failed to create transfer recipient: ' . $response['message']);
        }

        // Save recipient code for future use
        $details['paystack_recipient_code'] = $response['data']['recipient_code'];
        $withdrawal->update(['details' => $details]);

        return $response['data']['recipient_code'];
    }

    /**
     * Create vendor wallet if not exists
     */
    protected function createVendorWallet(Vendor $vendor): VendorWallet
    {
        return VendorWallet::create([
            'vendor_id' => $vendor->id,
        ]);
    }

    /**
     * Prepare withdrawal details based on method
     */
    protected function prepareWithdrawalDetails(array $data): array
    {
        $details = [];

        switch ($data['method']) {
            case 'bank_transfer':
                $details = [
                    'bank_name' => $data['bank_name'],
                    'bank_code' => $data['bank_code'],
                    'account_name' => $data['account_name'],
                    'account_number' => $data['account_number'],
                ];
                break;

            case 'paystack':
                $details = [
                    'email' => $data['paystack_email'],
                ];
                break;

            default:
                throw new Exception('Unsupported withdrawal method');
        }

        return $details;
    }

    /**
     * Generate unique withdrawal reference
     */
    protected function generateWithdrawalReference(): string
    {
        do {
            $reference = 'WD-' . strtoupper(Str::random(10));
        } while (Withdrawal::where('reference_number', $reference)->exists());

        return $reference;
    }
}
