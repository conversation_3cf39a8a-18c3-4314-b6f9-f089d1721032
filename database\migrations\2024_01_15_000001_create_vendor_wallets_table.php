<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_wallets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vendor_id')->constrained()->onDelete('cascade');
            $table->decimal('available_balance', 15, 2)->default(0)->comment('Balance available for withdrawal');
            $table->decimal('pending_balance', 15, 2)->default(0)->comment('Earnings pending delivery confirmation');
            $table->decimal('total_earnings', 15, 2)->default(0)->comment('Total lifetime earnings');
            $table->decimal('total_withdrawn', 15, 2)->default(0)->comment('Total amount withdrawn');
            $table->timestamp('last_updated_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->unique('vendor_id');
            $table->index(['vendor_id', 'available_balance']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_wallets');
    }
};
