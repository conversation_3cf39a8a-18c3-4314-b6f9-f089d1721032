<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ShipBubbleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class ShippingController extends Controller
{
    protected $shipBubbleService;

    public function __construct(ShipBubbleService $shipBubbleService)
    {
        $this->shipBubbleService = $shipBubbleService;
    }

    /**
     * Get shipping rates for checkout
     */
    public function getRates(Request $request)
    {
        try {
            $request->validate([
                'city' => 'required|string',
                'state' => 'required|string',
                'address' => 'required|string',
                'phone' => 'required|string',
                'email' => 'required|email',
                'name' => 'required|string',
            ]);

            // Get cart items from session
            $cartItems = Session::get('cart', []);
            
            if (empty($cartItems)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart is empty'
                ], 400);
            }

            $shippingAddress = [
                'name' => $request->name,
                'phone' => $request->phone,
                'email' => $request->email,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'country' => $request->country ?? 'Nigeria',
            ];

            $rates = $this->shipBubbleService->getShippingRatesForCheckout($cartItems, $shippingAddress);

            return response()->json([
                'success' => true,
                'data' => [
                    'rates' => $rates,
                    'cheapest_rate' => !empty($rates) ? min(array_column($rates, 'price')) : 0,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get shipping rates: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate shipping cost for a specific rate
     */
    public function calculateCost(Request $request)
    {
        try {
            $request->validate([
                'rate_id' => 'required|string',
                'city' => 'required|string',
                'state' => 'required|string',
            ]);

            $cartItems = Session::get('cart', []);
            
            if (empty($cartItems)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart is empty'
                ], 400);
            }

            $shippingAddress = [
                'city' => $request->city,
                'state' => $request->state,
                'country' => $request->country ?? 'Nigeria',
            ];

            $cost = $this->shipBubbleService->calculateShippingCost($cartItems, $shippingAddress);

            return response()->json([
                'success' => true,
                'data' => [
                    'shipping_cost' => $cost,
                    'formatted_cost' => '₦' . number_format($cost, 2),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate shipping cost: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available delivery locations
     */
    public function getLocations()
    {
        try {
            // Return Nigeria states for now
            $states = [
                'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno',
                'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT - Abuja', 'Gombe',
                'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos',
                'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto',
                'Taraba', 'Yobe', 'Zamfara'
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'states' => $states,
                    'country' => 'Nigeria'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get locations: ' . $e->getMessage()
            ], 500);
        }
    }
}
