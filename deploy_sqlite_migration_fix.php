<?php

/**
 * SQLite Migration Fix Deployment Script
 * 
 * This script applies the SQLite compatibility fixes for subscription_status field
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;

echo "🚀 Deploying SQLite Migration Fix...\n\n";

// Step 1: Check current environment
echo "1. Checking Environment...\n";
try {
    $driver = DB::getDriverName();
    $connection = DB::connection()->getName();
    
    echo "   - Database driver: {$driver}\n";
    echo "   - Connection: {$connection}\n";
    
    if ($driver === 'sqlite') {
        echo "   ✅ SQLite detected - applying SQLite-specific fixes\n";
    } else {
        echo "   ✅ {$driver} detected - applying database-agnostic fixes\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error checking environment: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 2: Verify migration files
echo "\n2. Verifying Migration Files...\n";

$requiredFiles = [
    'database/migrations/2025_06_05_190000_fix_sqlite_subscription_status.php',
    'app/Models/Vendor.php'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ {$file} exists\n";
    } else {
        echo "   ❌ {$file} missing\n";
        exit(1);
    }
}

// Step 3: Backup current state (if vendors table exists)
echo "\n3. Creating Backup...\n";

try {
    if (Schema::hasTable('vendors')) {
        $vendorCount = DB::table('vendors')->count();
        echo "   - Found {$vendorCount} vendors in database\n";
        
        // Create backup table
        DB::statement('CREATE TABLE IF NOT EXISTS vendors_migration_backup AS SELECT * FROM vendors WHERE 1=0');
        DB::statement('DELETE FROM vendors_migration_backup');
        DB::statement('INSERT INTO vendors_migration_backup SELECT * FROM vendors');
        
        $backupCount = DB::table('vendors_migration_backup')->count();
        echo "   ✅ Backed up {$backupCount} vendor records\n";
    } else {
        echo "   ℹ️  No vendors table found - no backup needed\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error creating backup: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 4: Run migrations
echo "\n4. Running Migrations...\n";

try {
    // Clear migration cache
    Artisan::call('migrate:status');
    
    // Run migrations
    $exitCode = Artisan::call('migrate', ['--force' => true]);
    
    if ($exitCode === 0) {
        echo "   ✅ Migrations completed successfully\n";
        $output = Artisan::output();
        if (trim($output)) {
            echo "   Migration output:\n" . $output . "\n";
        }
    } else {
        echo "   ❌ Migration failed with exit code: {$exitCode}\n";
        echo "   Output: " . Artisan::output() . "\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "   ❌ Error running migrations: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 5: Verify subscription_status field
echo "\n5. Verifying subscription_status Field...\n";

try {
    if (Schema::hasTable('vendors')) {
        if (Schema::hasColumn('vendors', 'subscription_status')) {
            echo "   ✅ subscription_status column exists\n";
            
            // Test if we can use 'cancelled' value
            $testVendor = DB::table('vendors')->first();
            if ($testVendor) {
                $originalStatus = $testVendor->subscription_status;
                
                // Test setting to 'cancelled'
                DB::table('vendors')
                    ->where('id', $testVendor->id)
                    ->update(['subscription_status' => 'cancelled']);
                
                $updatedVendor = DB::table('vendors')->where('id', $testVendor->id)->first();
                
                if ($updatedVendor->subscription_status === 'cancelled') {
                    echo "   ✅ Can set subscription_status to 'cancelled'\n";
                    
                    // Restore original status
                    DB::table('vendors')
                        ->where('id', $testVendor->id)
                        ->update(['subscription_status' => $originalStatus]);
                    
                    echo "   ✅ Original status restored\n";
                } else {
                    echo "   ❌ Cannot set subscription_status to 'cancelled'\n";
                }
            } else {
                echo "   ℹ️  No vendors found for testing\n";
            }
        } else {
            echo "   ❌ subscription_status column missing\n";
        }
        
        // Check cancelled_at column
        if (Schema::hasColumn('vendors', 'cancelled_at')) {
            echo "   ✅ cancelled_at column exists\n";
        } else {
            echo "   ⚠️  cancelled_at column missing\n";
        }
        
    } else {
        echo "   ❌ Vendors table missing\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error verifying fields: " . $e->getMessage() . "\n";
}

// Step 6: Test Vendor model validation
echo "\n6. Testing Vendor Model Validation...\n";

try {
    if (class_exists('App\Models\Vendor')) {
        $vendor = new \App\Models\Vendor();
        
        if (method_exists($vendor, 'getValidSubscriptionStatuses')) {
            $validStatuses = $vendor::getValidSubscriptionStatuses();
            echo "   ✅ Valid statuses: " . implode(', ', $validStatuses) . "\n";
            
            if (in_array('cancelled', $validStatuses)) {
                echo "   ✅ 'cancelled' status included in validation\n";
            } else {
                echo "   ❌ 'cancelled' status missing from validation\n";
            }
        } else {
            echo "   ❌ Validation methods missing\n";
        }
    } else {
        echo "   ❌ Vendor model not found\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error testing Vendor model: " . $e->getMessage() . "\n";
}

// Step 7: Clean up backup (optional)
echo "\n7. Cleanup...\n";

try {
    if (Schema::hasTable('vendors_migration_backup')) {
        $keepBackup = true; // Set to false to auto-delete backup
        
        if ($keepBackup) {
            echo "   ℹ️  Keeping backup table 'vendors_migration_backup' for safety\n";
            echo "   ℹ️  You can manually drop it later: DROP TABLE vendors_migration_backup;\n";
        } else {
            DB::statement('DROP TABLE vendors_migration_backup');
            echo "   ✅ Backup table cleaned up\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ⚠️  Error during cleanup: " . $e->getMessage() . "\n";
}

// Step 8: Generate summary
echo "\n8. Deployment Summary...\n";

$summary = [
    'database_driver' => $driver,
    'migration_status' => 'completed',
    'subscription_status_field' => Schema::hasColumn('vendors', 'subscription_status') ? 'exists' : 'missing',
    'cancelled_at_field' => Schema::hasColumn('vendors', 'cancelled_at') ? 'exists' : 'missing',
    'vendor_count' => Schema::hasTable('vendors') ? DB::table('vendors')->count() : 0,
    'timestamp' => date('Y-m-d H:i:s')
];

file_put_contents('sqlite_migration_deployment_report.json', json_encode($summary, JSON_PRETTY_PRINT));

echo "   📊 Deployment Report:\n";
foreach ($summary as $key => $value) {
    echo "     - " . str_replace('_', ' ', ucfirst($key)) . ": {$value}\n";
}

echo "\n🎉 SQLite Migration Fix Deployment Completed!\n";

echo "\nNext Steps:\n";
echo "1. Test vendor subscription cancellation functionality\n";
echo "2. Verify no SQLite syntax errors in logs\n";
echo "3. Test subscription status updates in application\n";
echo "4. Monitor for any database-related issues\n";

echo "\n✨ Your application now supports SQLite and MySQL databases!\n";
