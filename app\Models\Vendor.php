<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'shop_name',
        'slug',
        'description',
        'brand_description',
        'address',
        'city',
        'state',
        'country',
        'logo',
        'brand_logo',
        'approved',
        'is_featured',
        'subscription_status',
        'orders_processed',
        'free_order_limit',
        'subscription_started_at',
        'subscription_expires_at',
        'monthly_subscription_fee',
        'subscription_required',
        'cancelled_at',
    ];

    protected $casts = [
        'approved' => 'boolean',
        'is_featured' => 'boolean',
        'subscription_required' => 'boolean',
        'subscription_started_at' => 'datetime',
        'subscription_expires_at' => 'datetime',
        'monthly_subscription_fee' => 'decimal:2',
        'cancelled_at' => 'datetime',
    ];

    /**
     * Validation rules for vendor model
     */
    public static function validationRules(): array
    {
        return [
            'subscription_status' => 'required|in:active,inactive,pending_payment,expired,cancelled',
            'shop_name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:vendors,slug',
            'description' => 'nullable|string',
            'orders_processed' => 'integer|min:0',
            'free_order_limit' => 'integer|min:0',
            'monthly_subscription_fee' => 'numeric|min:0',
        ];
    }

    /**
     * Get valid subscription status values
     */
    public static function getValidSubscriptionStatuses(): array
    {
        return ['active', 'inactive', 'pending_payment', 'expired', 'cancelled'];
    }

    /**
     * Check if subscription status is valid
     */
    public function isValidSubscriptionStatus(string $status): bool
    {
        return in_array($status, self::getValidSubscriptionStatuses());
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function products()
    {
        return $this->hasMany(Product::class);
    }
    
    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }

    public function wallet()
    {
        return $this->hasOne(VendorWallet::class);
    }

    public function withdrawals()
    {
        return $this->hasMany(Withdrawal::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    
    public function subscription()
    {
        return $this->hasOneThrough(
            Subscription::class,
            User::class,
            'id', // Foreign key on users table...
            'user_id', // Foreign key on subscriptions table...
            'user_id', // Local key on vendors table...
            'id' // Local key on users table...
        );
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    public function getTotalSalesAttribute()
    {
        return $this->products()
            ->withSum('orderItems as total_sales', 'price_at_purchase * quantity')
            ->get()
            ->sum('total_sales');
    }

    /**
     * Check if vendor is in platform delivery zone
     */
    public function isPlatformDeliveryZone(): bool
    {
        return $this->delivery_zone_type === 'platform_delivery_zone';
    }

    /**
     * Check if vendor has active subscription
     */
    public function hasActiveSubscription(): bool
    {
        // Active or cancelled (but not expired) subscriptions allow order processing
        return in_array($this->subscription_status, ['active', 'cancelled']);
    }

    /**
     * Get vendor's business name or shop name
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->business_name ?: $this->shop_name;
    }

    /**
     * Get vendor's primary address
     */
    public function getPrimaryAddressAttribute(): string
    {
        return $this->address_line1 ?: $this->address;
    }

    /**
     * Get vendor's contact email
     */
    public function getContactEmailAttribute(): string
    {
        return $this->contact_email_business ?: $this->user->email;
    }

    /**
     * Get brand name (same as shop name since vendors are brands)
     */
    public function getBrandNameAttribute(): string
    {
        return $this->shop_name;
    }

    /**
     * Get brand description (use brand_description if available, otherwise description)
     */
    public function getBrandDescriptionAttribute(): string
    {
        return $this->attributes['brand_description'] ?? $this->description;
    }

    /**
     * Get brand logo (use brand_logo if available, otherwise logo)
     */
    public function getBrandLogoAttribute(): string
    {
        return $this->attributes['brand_logo'] ?? $this->logo;
    }

    /**
     * Scope for featured brands (vendors)
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for active brands (vendors)
     */
    public function scopeActive($query)
    {
        return $query->where('approved', true);
    }

    /**
     * Check if vendor has reached their free order limit
     */
    public function hasReachedFreeOrderLimit(): bool
    {
        return $this->orders_processed >= $this->free_order_limit;
    }

    /**
     * Check if vendor needs to subscribe to continue processing orders
     */
    public function needsSubscription(): bool
    {
        return $this->hasReachedFreeOrderLimit() && !$this->hasActiveSubscription();
    }

    /**
     * Get remaining free orders
     */
    public function getRemainingFreeOrders(): int
    {
        return max(0, $this->free_order_limit - $this->orders_processed);
    }

    /**
     * Increment the order count for this vendor
     */
    public function incrementOrderCount(): void
    {
        $this->increment('orders_processed');

        // Check if vendor needs subscription after this order
        if ($this->hasReachedFreeOrderLimit() && !$this->subscription_required) {
            $this->update(['subscription_required' => true]);
        }
    }

    /**
     * Check if vendor can process new orders
     */
    public function canProcessOrders(): bool
    {
        // If vendor hasn't reached free limit, they can process orders
        if (!$this->hasReachedFreeOrderLimit()) {
            return true;
        }

        // If they have reached limit, they need an active subscription
        // Cancelled subscriptions can still process orders until expiration
        return $this->hasActiveSubscription() && $this->isSubscriptionValid();
    }

    /**
     * Check if subscription is valid (not expired)
     */
    public function isSubscriptionValid(): bool
    {
        if (!$this->subscription_expires_at) {
            return true; // No expiration date means lifetime or manual management
        }

        return $this->subscription_expires_at->isFuture();
    }

    /**
     * Activate subscription for vendor
     */
    public function activateSubscription(int $durationDays = 30): void
    {
        $this->update([
            'subscription_status' => 'active',
            'subscription_started_at' => now(),
            'subscription_expires_at' => now()->addDays($durationDays),
            'subscription_required' => false,
        ]);
    }

    /**
     * Get subscription status with details
     */
    public function getSubscriptionStatusDetails(): array
    {
        return [
            'status' => $this->subscription_status,
            'orders_processed' => $this->orders_processed,
            'free_orders_remaining' => $this->getRemainingFreeOrders(),
            'needs_subscription' => $this->needsSubscription(),
            'can_process_orders' => $this->canProcessOrders(),
            'subscription_expires_at' => $this->subscription_expires_at,
            'is_subscription_valid' => $this->isSubscriptionValid(),
        ];
    }
}
