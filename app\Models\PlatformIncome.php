<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlatformIncome extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'amount',
        'source_id',
        'source_type',
        'description',
        'metadata',
        'recorded_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'metadata' => 'array',
        'recorded_at' => 'datetime',
    ];

    /**
     * Income types
     */
    const TYPE_COMMISSION = 'commission';
    const TYPE_SUBSCRIPTION = 'subscription';
    const TYPE_PAYMENT_FEE = 'payment_fee';
    const TYPE_WITHDRAWAL_FEE = 'withdrawal_fee';
    const TYPE_SHIPPING_FEE = 'shipping_fee';
    const TYPE_LISTING_FEE = 'listing_fee';
    const TYPE_OTHER = 'other';

    /**
     * Get the source model (polymorphic relationship)
     */
    public function source()
    {
        return $this->morphTo();
    }

    /**
     * Get formatted amount with currency symbol
     */
    public function getFormattedAmountAttribute(): string
    {
        $symbol = config('ecommerce.commission.currency_symbol', '₦');
        return "{$symbol}" . number_format($this->amount, 2);
    }

    /**
     * Get type label
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            self::TYPE_COMMISSION => 'Platform Commission',
            self::TYPE_SUBSCRIPTION => 'Subscription Revenue',
            self::TYPE_PAYMENT_FEE => 'Payment Processing Fee',
            self::TYPE_WITHDRAWAL_FEE => 'Withdrawal Processing Fee',
            self::TYPE_SHIPPING_FEE => 'Shipping Fee',
            self::TYPE_LISTING_FEE => 'Product Listing Fee',
            self::TYPE_OTHER => 'Other Income',
            default => ucfirst(str_replace('_', ' ', $this->type)),
        };
    }

    /**
     * Get type badge class for UI
     */
    public function getTypeBadgeClass(): string
    {
        return match($this->type) {
            self::TYPE_COMMISSION => 'badge-primary',
            self::TYPE_SUBSCRIPTION => 'badge-success',
            self::TYPE_PAYMENT_FEE => 'badge-info',
            self::TYPE_WITHDRAWAL_FEE => 'badge-warning',
            self::TYPE_SHIPPING_FEE => 'badge-secondary',
            self::TYPE_LISTING_FEE => 'badge-dark',
            self::TYPE_OTHER => 'badge-light',
            default => 'badge-secondary',
        };
    }

    /**
     * Scope for filtering by type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('recorded_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent records
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('recorded_at', '>=', now()->subDays($days));
    }

    /**
     * Get valid income types
     */
    public static function getValidTypes(): array
    {
        return [
            self::TYPE_COMMISSION,
            self::TYPE_SUBSCRIPTION,
            self::TYPE_PAYMENT_FEE,
            self::TYPE_WITHDRAWAL_FEE,
            self::TYPE_SHIPPING_FEE,
            self::TYPE_LISTING_FEE,
            self::TYPE_OTHER,
        ];
    }

    /**
     * Record commission income from an order
     */
    public static function recordCommission(Order $order, float $amount): self
    {
        return self::create([
            'type' => self::TYPE_COMMISSION,
            'amount' => $amount,
            'source_id' => $order->id,
            'source_type' => Order::class,
            'description' => "Platform commission from order #{$order->order_number}",
            'metadata' => [
                'order_number' => $order->order_number,
                'vendor_id' => $order->vendor_id,
                'commission_rate' => config('ecommerce.commission.rate'),
            ],
            'recorded_at' => now(),
        ]);
    }

    /**
     * Record subscription income from a vendor
     */
    public static function recordSubscription(Vendor $vendor, float $amount): self
    {
        return self::create([
            'type' => self::TYPE_SUBSCRIPTION,
            'amount' => $amount,
            'source_id' => $vendor->id,
            'source_type' => Vendor::class,
            'description' => "Monthly subscription fee from vendor: {$vendor->shop_name}",
            'metadata' => [
                'vendor_id' => $vendor->id,
                'shop_name' => $vendor->shop_name,
                'subscription_period' => now()->format('Y-m'),
            ],
            'recorded_at' => now(),
        ]);
    }

    /**
     * Record payment processing fee
     */
    public static function recordPaymentFee(Order $order, float $amount): self
    {
        return self::create([
            'type' => self::TYPE_PAYMENT_FEE,
            'amount' => $amount,
            'source_id' => $order->id,
            'source_type' => Order::class,
            'description' => "Payment processing fee for order #{$order->order_number}",
            'metadata' => [
                'order_number' => $order->order_number,
                'payment_method' => $order->payment_method,
                'order_total' => $order->total,
            ],
            'recorded_at' => now(),
        ]);
    }

    /**
     * Record withdrawal processing fee
     */
    public static function recordWithdrawalFee(Withdrawal $withdrawal, float $amount): self
    {
        return self::create([
            'type' => self::TYPE_WITHDRAWAL_FEE,
            'amount' => $amount,
            'source_id' => $withdrawal->id,
            'source_type' => Withdrawal::class,
            'description' => "Withdrawal processing fee for #{$withdrawal->reference_number}",
            'metadata' => [
                'withdrawal_reference' => $withdrawal->reference_number,
                'vendor_id' => $withdrawal->vendor_id,
                'withdrawal_amount' => $withdrawal->amount,
                'withdrawal_method' => $withdrawal->method,
            ],
            'recorded_at' => now(),
        ]);
    }

    /**
     * Get income summary for a date range
     */
    public static function getIncomeSummary($startDate, $endDate): array
    {
        $incomes = self::selectRaw('type, SUM(amount) as total_amount, COUNT(*) as count')
            ->whereBetween('recorded_at', [$startDate, $endDate])
            ->groupBy('type')
            ->get()
            ->keyBy('type');

        $summary = [];
        foreach (self::getValidTypes() as $type) {
            $income = $incomes->get($type);
            $summary[$type] = [
                'amount' => $income ? $income->total_amount : 0,
                'count' => $income ? $income->count : 0,
            ];
        }

        $summary['total'] = [
            'amount' => $incomes->sum('total_amount'),
            'count' => $incomes->sum('count'),
        ];

        return $summary;
    }

    /**
     * Get monthly income trend
     */
    public static function getMonthlyTrend(int $months = 12): array
    {
        $startDate = now()->subMonths($months)->startOfMonth();
        $endDate = now()->endOfMonth();

        return self::selectRaw('DATE_FORMAT(recorded_at, "%Y-%m") as month, type, SUM(amount) as total_amount')
            ->whereBetween('recorded_at', [$startDate, $endDate])
            ->groupBy('month', 'type')
            ->orderBy('month')
            ->get()
            ->groupBy('month')
            ->map(function ($monthData) {
                $result = ['total' => 0];
                foreach (self::getValidTypes() as $type) {
                    $typeData = $monthData->firstWhere('type', $type);
                    $result[$type] = $typeData ? $typeData->total_amount : 0;
                    $result['total'] += $result[$type];
                }
                return $result;
            })
            ->toArray();
    }
}
