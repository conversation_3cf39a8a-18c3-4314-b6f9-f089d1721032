@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="fw-bold mb-1">My Wishlist</h2>
                    <p class="text-muted mb-0">{{ $wishlistItems->total() }} {{ Str::plural('item', $wishlistItems->total()) }} saved</p>
                </div>
                @if($wishlistItems->count() > 0)
                    <div>
                        <form action="{{ route('wishlist.clear') }}" method="POST" class="d-inline" 
                              onsubmit="return confirm('Are you sure you want to clear your entire wishlist?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger">
                                <i class="fas fa-trash me-2"></i>Clear All
                            </button>
                        </form>
                    </div>
                @endif
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('info'))
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    {{ session('info') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Wishlist Items -->
            @if($wishlistItems->count() > 0)
                <div class="row">
                    @foreach($wishlistItems as $item)
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm hover-shadow position-relative">
                                <!-- Wishlist Remove Button -->
                                <button class="btn btn-sm btn-danger position-absolute top-0 end-0 m-2 rounded-circle wishlist-remove-btn" 
                                        data-product-id="{{ $item->product->id }}" 
                                        style="width: 32px; height: 32px; z-index: 10;">
                                    <i class="fas fa-times"></i>
                                </button>

                                <!-- Product Image -->
                                <div class="position-relative overflow-hidden">
                                    <img src="{{ $item->product->image_url ?? 'https://via.placeholder.com/300x300?text=Product' }}" 
                                         class="card-img-top" 
                                         alt="{{ $item->product->name }}"
                                         style="height: 250px; object-fit: cover;">
                                    
                                    @if($item->product->stock_quantity <= 0)
                                        <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-75">
                                            <span class="badge bg-danger fs-6">Out of Stock</span>
                                        </div>
                                    @endif
                                </div>

                                <div class="card-body p-3">
                                    <!-- Vendor Name -->
                                    @if($item->product->vendor)
                                        <p class="text-muted small mb-1">{{ $item->product->vendor->shop_name }}</p>
                                    @endif

                                    <!-- Product Name -->
                                    <h6 class="card-title fw-bold mb-2">
                                        <a href="{{ route('products.show', $item->product->slug) }}" 
                                           class="text-decoration-none text-dark">
                                            {{ Str::limit($item->product->name, 50) }}
                                        </a>
                                    </h6>

                                    <!-- Category -->
                                    @if($item->product->category)
                                        <p class="text-muted small mb-2">{{ $item->product->category->name }}</p>
                                    @endif

                                    <!-- Price -->
                                    <div class="d-flex align-items-center justify-content-between mb-3">
                                        <div>
                                            <span class="fw-bold text-primary fs-5">₦{{ number_format($item->product->price, 2) }}</span>
                                            @if($item->product->compare_price && $item->product->compare_price > $item->product->price)
                                                <small class="text-muted text-decoration-line-through ms-2">
                                                    ₦{{ number_format($item->product->compare_price, 2) }}
                                                </small>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="d-grid gap-2">
                                        @if($item->product->stock_quantity > 0)
                                            <button class="btn btn-dark add-to-cart-btn" 
                                                    data-product-id="{{ $item->product->id }}"
                                                    data-product-name="{{ $item->product->name }}"
                                                    data-product-price="{{ $item->product->price }}"
                                                    data-product-image="{{ $item->product->image_url }}"
                                                    data-product-slug="{{ $item->product->slug }}">
                                                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                            </button>
                                        @else
                                            <button class="btn btn-secondary" disabled>
                                                <i class="fas fa-times me-2"></i>Out of Stock
                                            </button>
                                        @endif
                                        
                                        <a href="{{ route('products.show', $item->product->slug) }}" 
                                           class="btn btn-outline-dark">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a>
                                    </div>
                                </div>

                                <!-- Added Date -->
                                <div class="card-footer bg-light border-0 py-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>Added {{ $item->created_at->diffForHumans() }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $wishlistItems->links() }}
                </div>
            @else
                <!-- Empty Wishlist -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-heart fa-5x text-muted"></i>
                    </div>
                    <h3 class="fw-bold mb-3">Your Wishlist is Empty</h3>
                    <p class="text-muted mb-4">Save items you love to your wishlist and shop them later.</p>
                    <a href="{{ route('products.index') }}" class="btn btn-dark btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="wishlistToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="fas fa-heart text-danger me-2"></i>
            <strong class="me-auto">Wishlist</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body"></div>
    </div>
</div>
@endsection

@push('styles')
<style>
.hover-shadow {
    transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.wishlist-remove-btn {
    transition: all 0.3s ease;
}

.wishlist-remove-btn:hover {
    transform: scale(1.1);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Remove from wishlist
    document.querySelectorAll('.wishlist-remove-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            
            fetch('{{ route("wishlist.remove") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    product_id: productId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the card from DOM
                    this.closest('.col-lg-3').remove();
                    
                    // Show toast
                    showToast(data.message, 'success');
                    
                    // Update page if no items left
                    if (document.querySelectorAll('.wishlist-remove-btn').length === 0) {
                        location.reload();
                    }
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('An error occurred. Please try again.', 'error');
            });
        });
    });

    // Add to cart
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productData = {
                id: this.dataset.productId,
                name: this.dataset.productName,
                price: parseFloat(this.dataset.productPrice),
                image: this.dataset.productImage,
                slug: this.dataset.productSlug
            };

            // Add to cart logic here (implement based on your cart system)
            addToCart(productData);
        });
    });

    function showToast(message, type) {
        const toast = document.getElementById('wishlistToast');
        const toastBody = toast.querySelector('.toast-body');
        
        toastBody.textContent = message;
        
        // Update toast styling based on type
        const toastHeader = toast.querySelector('.toast-header');
        if (type === 'success') {
            toastHeader.className = 'toast-header bg-success text-white';
        } else {
            toastHeader.className = 'toast-header bg-danger text-white';
        }
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    function addToCart(product) {
        // Implement your cart addition logic here
        // This should match your existing cart system
        showToast(`${product.name} added to cart!`, 'success');
    }
});
</script>
@endpush
