<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix 1: Ensure vendors table has consistent column naming
        if (Schema::hasTable('vendors')) {
            // Check if we need to rename is_approved to approved
            if (Schema::hasColumn('vendors', 'is_approved') && !Schema::hasColumn('vendors', 'approved')) {
                Schema::table('vendors', function (Blueprint $table) {
                    $table->renameColumn('is_approved', 'approved');
                });
            }
            
            // Ensure approved column exists and is boolean
            if (!Schema::hasColumn('vendors', 'approved')) {
                Schema::table('vendors', function (Blueprint $table) {
                    $table->boolean('approved')->default(false)->after('logo');
                });
            }
        }

        // Fix 2: Ensure users table has both role and role_id fields for compatibility
        if (Schema::hasTable('users')) {
            // Add role field if it doesn't exist
            if (!Schema::hasColumn('users', 'role')) {
                Schema::table('users', function (Blueprint $table) {
                    $table->string('role')->default('customer')->after('password');
                });
                
                // Populate role field based on role_id
                DB::statement("
                    UPDATE users 
                    SET role = CASE 
                        WHEN role_id = 1 THEN 'admin'
                        WHEN role_id = 2 THEN 'vendor'
                        ELSE 'customer'
                    END
                ");
            }
        }

        // Fix 3: Ensure subscription_status enum includes all required values
        if (Schema::hasTable('vendors') && Schema::hasColumn('vendors', 'subscription_status')) {
            // For SQLite compatibility, we'll handle this differently
            $driver = DB::getDriverName();
            
            if ($driver === 'sqlite') {
                // SQLite doesn't support modifying enum, so we'll ensure data integrity
                DB::statement("
                    UPDATE vendors 
                    SET subscription_status = 'pending_payment' 
                    WHERE subscription_status NOT IN ('active', 'inactive', 'pending_payment', 'expired', 'cancelled')
                ");
            } else {
                // For MySQL, modify the enum constraint
                DB::statement("
                    ALTER TABLE vendors 
                    MODIFY COLUMN subscription_status ENUM('active', 'inactive', 'pending_payment', 'expired', 'cancelled') 
                    DEFAULT 'pending_payment'
                ");
            }
        }

        // Fix 4: Ensure all required vendor fields exist
        if (Schema::hasTable('vendors')) {
            Schema::table('vendors', function (Blueprint $table) {
                if (!Schema::hasColumn('vendors', 'orders_processed')) {
                    $table->integer('orders_processed')->default(0)->after('subscription_status');
                }
                if (!Schema::hasColumn('vendors', 'free_order_limit')) {
                    $table->integer('free_order_limit')->default(10)->after('orders_processed');
                }
                if (!Schema::hasColumn('vendors', 'subscription_required')) {
                    $table->boolean('subscription_required')->default(false)->after('free_order_limit');
                }
                if (!Schema::hasColumn('vendors', 'cancelled_at')) {
                    $table->timestamp('cancelled_at')->nullable()->after('subscription_required');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the changes if needed
        if (Schema::hasTable('vendors')) {
            Schema::table('vendors', function (Blueprint $table) {
                if (Schema::hasColumn('vendors', 'cancelled_at')) {
                    $table->dropColumn('cancelled_at');
                }
                if (Schema::hasColumn('vendors', 'subscription_required')) {
                    $table->dropColumn('subscription_required');
                }
                if (Schema::hasColumn('vendors', 'free_order_limit')) {
                    $table->dropColumn('free_order_limit');
                }
                if (Schema::hasColumn('vendors', 'orders_processed')) {
                    $table->dropColumn('orders_processed');
                }
            });
        }

        if (Schema::hasTable('users') && Schema::hasColumn('users', 'role')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('role');
            });
        }
    }
};
