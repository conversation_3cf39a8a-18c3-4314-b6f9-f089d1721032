<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Commission;
use App\Models\Product;
use App\Services\CommissionService;
use App\Services\ShipBubbleService;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    /**
     * Initialize Paystack payment
     */
    public function initializePaystack(Request $request)
    {
        // Validate the request
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'postal_code' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'terms' => 'required',
        ]);
        
        // Calculate order totals
        $cartItems = Session::get('cart', []);
        
        if (empty($cartItems)) {
            return redirect()->route('cart.index')
                ->with('error', 'Your cart is empty. Please add products to your cart.');
        }

        // Determine vendor_id from the first cart item
        $firstCartItem = reset($cartItems); // Get the first item
        $vendorId = null;
        if ($firstCartItem && isset($firstCartItem['vendor_id'])) {
            $vendorId = $firstCartItem['vendor_id'];
        } else {
            // Attempt to get vendor_id from product if not directly in cart item, or if cart structure is different
            // This is a fallback, ideally vendor_id should be consistently in the cart item structure
            if ($firstCartItem && isset($firstCartItem['product_id'])) { // Assuming product_id is the key for product's actual ID
                $product = \App\Models\Product::find($firstCartItem['product_id']);
                if ($product) {
                    $vendorId = $product->vendor_id;
                }
            }
        }

        if (is_null($vendorId)) {
            // Log this issue or handle more gracefully
            // For now, redirect back with an error if no vendor_id can be determined
            
            // Check if the key for product ID is just 'id' instead of 'product_id'
            // This part is speculative based on common cart structures
            if ($firstCartItem && isset($firstCartItem['id'])) {
                 $product = \App\Models\Product::find($firstCartItem['id']);
                 if ($product) {
                    $vendorId = $product->vendor_id;
                 }
            }
            if (is_null($vendorId)) { // Re-check after attempting with 'id'
                 return redirect()->route('cart.index')
                    ->with('error', 'Could not determine vendor for the order. Please try again or contact support.');
            }
        }
        
        $subtotal = 0;
        foreach ($cartItems as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }

        $tax = $subtotal * 0.08; // 8% tax

        // Calculate shipping using ShipBubble
        $shippingAddress = [
            'name' => $request->first_name . ' ' . $request->last_name,
            'phone' => $request->phone,
            'email' => $request->email,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country ?? 'Nigeria',
        ];

        $shipBubbleService = new \App\Services\ShipBubbleService();
        $shipping = $shipBubbleService->calculateShippingCost($cartItems, $shippingAddress);

        $total = $subtotal + $tax + $shipping;
        
        // Create the order in pending status
        $order = Order::create([
            'user_id' => auth()->id(),
            'vendor_id' => $vendorId,
            'order_number' => 'ORD-' . Str::random(10),
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => 'paystack',
            'total_amount' => $total, // Fixed: Use total_amount instead of total
            'shipping_address' => json_encode([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postal_code' => $request->postal_code,
                'country' => $request->country,
                'notes' => $request->order_notes ?? ''
            ]),
            'shipping_name' => $request->first_name . ' ' . $request->last_name,
            'shipping_city' => $request->city,
            'shipping_state' => $request->state,
            'shipping_postal_code' => $request->postal_code,
            'shipping_country' => $request->country,
            'shipping_phone' => $request->phone,
        ]);
        
        // Create order items and calculate commissions
        foreach ($cartItems as $id => $item) {
            $orderItem = OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $id,
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'product_data' => json_encode(['name' => $item['name']])
            ]);
            
            // Create commission record for the vendor
            $product = \App\Models\Product::find($id);
            if ($product && $product->vendor) {
                $commissionRate = 0.10; // 10% commission to the platform
                $commissionAmount = $item['price'] * $item['quantity'] * $commissionRate;
                
                Commission::create([
                    'vendor_id' => $product->vendor_id,
                    'order_id' => $order->id,
                    'order_item_id' => $orderItem->id,
                    'order_amount' => $item['price'] * $item['quantity'],
                    'amount' => $commissionAmount,
                    'status' => 'pending'
                ]);
            }
        }
        
        // Generate Paystack payment data
        $amount = $total * 100; // Convert to kobo (Paystack uses the smallest currency unit)
        $email = $request->email;
        $reference = Str::random(16);
        $callback_url = route('payment.paystack.callback');
        $metadata = json_encode([
            'order_id' => $order->id,
            'order_number' => $order->order_number,
            'custom_fields' => [
                [
                    'display_name' => 'Order Number',
                    'variable_name' => 'order_number',
                    'value' => $order->order_number
                ]
            ]
        ]);
        
        // Store reference in the session
        Session::put('paystack_reference', $reference);
        Session::put('order_id', $order->id);
        
        // Prepare Paystack URL
        $paystack_url = "https://api.paystack.co/transaction/initialize";
        
        // Set Paystack API Key - This should be in your .env file
        $secret_key = config('services.paystack.secret_key');
        
        // Prepare the fields
        $fields = [
            'email' => $email,
            'amount' => $amount,
            'reference' => $reference,
            'callback_url' => $callback_url,
            'metadata' => $metadata
        ];
        
        // Make request to Paystack API
        $fields_string = http_build_query($fields);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $paystack_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer " . $secret_key,
            "Cache-Control: no-cache"
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);
        
        $result = json_decode($response, true);
        
        // If initialization was successful, redirect to Paystack payment page
        if ($result && $result['status']) {
            // Empty the cart after creating the order
            Session::forget('cart');
            
            return redirect($result['data']['authorization_url']);
        } else {
            // If there was an error, show error message
            return redirect()->route('checkout.index')
                ->with('error', 'Unable to initialize payment. Please try again later.');
        }
    }
    
    /**
     * Handle Paystack payment callback
     */
    public function handlePaystackCallback(Request $request)
    {
        $reference = $request->reference;
        if (!$reference) {
            return redirect()->route('checkout.index')
                ->with('error', 'Payment was not successful. Please try again.');
        }
        
        // Verify transaction with Paystack
        $secret_key = config('services.paystack.secret_key');
        $curl = curl_init();
        
        curl_setopt_array($curl, [
            CURLOPT_URL => "https://api.paystack.co/transaction/verify/" . rawurlencode($reference),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer " . $secret_key,
                "Cache-Control: no-cache"
            ],
        ]);
        
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        
        if ($err) {
            // There was an error during the verification
            return redirect()->route('checkout.index')
                ->with('error', 'Payment verification failed. Please contact support.');
        }
        
        $transaction = json_decode($response);
        
        // Check if transaction was successful
        if ($transaction->status && $transaction->data->status === 'success') {
            // Get the order ID from session
            $order_id = Session::get('order_id');
            $order = Order::find($order_id);
            
            if ($order) {
                // Update order status
                $order->payment_status = 'paid';
                $order->status = 'processing';
                $order->payment_details = json_encode([
                    'gateway' => 'paystack',
                    'reference' => $reference,
                    'amount' => $transaction->data->amount / 100, // Convert back from kobo
                    'paid_at' => now()->toDateTimeString()
                ]);
                $order->save();
                
                // Clear session data
                Session::forget('paystack_reference');
                Session::forget('order_id');
                
                // Redirect to success page
                return redirect()->route('checkout.success', ['order' => $order->id])
                    ->with('success', 'Payment was successful! Your order is now being processed.');
            }
        }
        
        // If payment failed or order not found
        return redirect()->route('checkout.index')
            ->with('error', 'Payment was not successful. Please try again.');
    }
    
    /**
     * Display checkout success page
     */
    public function checkoutSuccess(Order $order)
    {
        // Ensure the order belongs to the authenticated user
        if ($order->user_id !== auth()->id()) {
            abort(403, 'Unauthorized action.');
        }
        
        return view('checkout.success', compact('order'));
    }

    /**
     * Handle Paystack webhook for charge.success events
     */
    public function handlePaystackWebhook(Request $request)
    {
        // Verify webhook signature
        $signature = $request->header('x-paystack-signature');
        $webhookSecret = config('services.paystack.webhook_secret');

        if (!$signature || !$webhookSecret) {
            Log::warning('Paystack webhook: Missing signature or secret');
            return response('Unauthorized', 401);
        }

        $computedSignature = hash_hmac('sha512', $request->getContent(), $webhookSecret);

        if (!hash_equals($signature, $computedSignature)) {
            Log::warning('Paystack webhook: Invalid signature');
            return response('Unauthorized', 401);
        }

        $event = $request->all();

        switch ($event['event']) {
            case 'charge.success':
                $this->handleSuccessfulPayment($event['data']);
                break;
            case 'subscription.create':
                $this->handleSubscriptionCreated($event['data']);
                break;
            case 'subscription.disable':
                $this->handleSubscriptionCancelled($event['data']);
                break;
            case 'invoice.payment_failed':
                $this->handleSubscriptionPaymentFailed($event['data']);
                break;
            case 'transfer.success':
                $this->handleTransferSuccess($event['data']);
                break;
            case 'transfer.failed':
                $this->handleTransferFailed($event['data']);
                break;
            case 'transfer.reversed':
                $this->handleTransferReversed($event['data']);
                break;
        }

        return response('OK', 200);
    }

    /**
     * Handle successful payment processing
     */
    protected function handleSuccessfulPayment(array $paymentData)
    {
        try {
            $reference = $paymentData['reference'];
            $metadata = $paymentData['metadata'] ?? [];
            $orderId = $metadata['order_id'] ?? null;

            if (!$orderId) {
                Log::error('Paystack webhook: No order ID in metadata', ['reference' => $reference]);
                return;
            }

            $order = Order::find($orderId);
            if (!$order) {
                Log::error('Paystack webhook: Order not found', ['order_id' => $orderId]);
                return;
            }

            // Update order status
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
                'payment_gateway_reference' => $reference
            ]);

            // Calculate and record commissions
            $commissionService = new CommissionService();
            $commissionService->calculateOrderCommission($order);

            // Decrement product stock
            foreach ($order->items as $item) {
                $product = $item->product;
                if ($product) {
                    $product->decrement('stock_quantity', $item->quantity);
                }
            }

            // Trigger ShipBubbles integration for platform delivery items
            $shipBubbleService = new ShipBubbleService();
            $shipBubbleService->createShipment($order);

            // TODO: Send notifications to customer and vendor

            Log::info('Payment processed successfully', [
                'order_id' => $order->id,
                'reference' => $reference
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing successful payment', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData
            ]);
        }
    }

    /**
     * Handle subscription created webhook
     */
    protected function handleSubscriptionCreated(array $subscriptionData)
    {
        try {
            $metadata = $subscriptionData['metadata'] ?? [];
            $vendorId = $metadata['vendor_id'] ?? null;

            if (!$vendorId) {
                Log::error('Paystack webhook: No vendor ID in subscription metadata');
                return;
            }

            $vendor = \App\Models\Vendor::find($vendorId);
            if (!$vendor) {
                Log::error('Paystack webhook: Vendor not found', ['vendor_id' => $vendorId]);
                return;
            }

            // Activate subscription
            $vendor->update([
                'subscription_status' => 'active',
                'subscription_started_at' => now(),
                'subscription_expires_at' => now()->addDays(30), // Default 30 days
                'subscription_required' => false,
            ]);

            Log::info('Subscription activated via webhook', ['vendor_id' => $vendorId]);

        } catch (\Exception $e) {
            Log::error('Paystack webhook: Failed to process subscription creation', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Handle subscription cancelled webhook
     */
    protected function handleSubscriptionCancelled(array $subscriptionData)
    {
        try {
            $metadata = $subscriptionData['metadata'] ?? [];
            $vendorId = $metadata['vendor_id'] ?? null;

            if (!$vendorId) {
                Log::error('Paystack webhook: No vendor ID in subscription metadata');
                return;
            }

            $vendor = \App\Models\Vendor::find($vendorId);
            if (!$vendor) {
                Log::error('Paystack webhook: Vendor not found', ['vendor_id' => $vendorId]);
                return;
            }

            // Mark subscription as cancelled but keep access until expiration
            $vendor->update([
                'subscription_status' => 'cancelled',
                'cancelled_at' => now(),
            ]);

            Log::info('Subscription cancelled via webhook', ['vendor_id' => $vendorId]);

        } catch (\Exception $e) {
            Log::error('Paystack webhook: Failed to process subscription cancellation', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Handle subscription payment failed webhook
     */
    protected function handleSubscriptionPaymentFailed(array $invoiceData)
    {
        try {
            $metadata = $invoiceData['metadata'] ?? [];
            $vendorId = $metadata['vendor_id'] ?? null;

            if (!$vendorId) {
                Log::error('Paystack webhook: No vendor ID in invoice metadata');
                return;
            }

            $vendor = \App\Models\Vendor::find($vendorId);
            if (!$vendor) {
                Log::error('Paystack webhook: Vendor not found', ['vendor_id' => $vendorId]);
                return;
            }

            // Mark subscription as pending payment
            $vendor->update([
                'subscription_status' => 'pending_payment',
            ]);

            Log::info('Subscription payment failed via webhook', ['vendor_id' => $vendorId]);

        } catch (\Exception $e) {
            Log::error('Paystack webhook: Failed to process subscription payment failure', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Handle transfer success webhook
     */
    protected function handleTransferSuccess(array $transferData)
    {
        try {
            $reference = $transferData['reference'] ?? null;

            if (!$reference) {
                Log::error('Paystack webhook: No reference in transfer data');
                return;
            }

            $withdrawalService = app(\App\Services\WithdrawalService::class);
            $withdrawalService->completeWithdrawal($reference);

            Log::info('Transfer completed via webhook', ['reference' => $reference]);

        } catch (\Exception $e) {
            Log::error('Paystack webhook: Failed to process transfer success', [
                'error' => $e->getMessage(),
                'transfer_data' => $transferData
            ]);
        }
    }

    /**
     * Handle transfer failed webhook
     */
    protected function handleTransferFailed(array $transferData)
    {
        try {
            $reference = $transferData['reference'] ?? null;
            $reason = $transferData['failures']['reason'] ?? 'Transfer failed';

            if (!$reference) {
                Log::error('Paystack webhook: No reference in transfer data');
                return;
            }

            $withdrawalService = app(\App\Services\WithdrawalService::class);
            $withdrawalService->failWithdrawal($reference, $reason);

            Log::info('Transfer failed via webhook', [
                'reference' => $reference,
                'reason' => $reason
            ]);

        } catch (\Exception $e) {
            Log::error('Paystack webhook: Failed to process transfer failure', [
                'error' => $e->getMessage(),
                'transfer_data' => $transferData
            ]);
        }
    }

    /**
     * Handle transfer reversed webhook
     */
    protected function handleTransferReversed(array $transferData)
    {
        try {
            $reference = $transferData['reference'] ?? null;

            if (!$reference) {
                Log::error('Paystack webhook: No reference in transfer data');
                return;
            }

            $withdrawalService = app(\App\Services\WithdrawalService::class);
            $withdrawalService->failWithdrawal($reference, 'Transfer was reversed');

            Log::info('Transfer reversed via webhook', ['reference' => $reference]);

        } catch (\Exception $e) {
            Log::error('Paystack webhook: Failed to process transfer reversal', [
                'error' => $e->getMessage(),
                'transfer_data' => $transferData
            ]);
        }
    }
}
