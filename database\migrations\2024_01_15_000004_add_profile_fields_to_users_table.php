<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Personal Information
            $table->string('phone')->nullable()->after('email');
            $table->date('date_of_birth')->nullable()->after('phone');
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('date_of_birth');
            $table->text('bio')->nullable()->after('gender');
            $table->string('avatar')->nullable()->after('bio');
            
            // Notification Preferences
            $table->boolean('email_notifications')->default(true)->after('avatar');
            $table->boolean('order_updates')->default(true)->after('email_notifications');
            $table->boolean('promotional_emails')->default(false)->after('order_updates');
            $table->boolean('security_alerts')->default(true)->after('promotional_emails');
            
            // Privacy Settings
            $table->enum('profile_visibility', ['public', 'private'])->default('public')->after('security_alerts');
            $table->boolean('show_email')->default(false)->after('profile_visibility');
            $table->boolean('show_phone')->default(false)->after('show_email');
            $table->boolean('allow_messages')->default(true)->after('show_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'date_of_birth',
                'gender',
                'bio',
                'avatar',
                'email_notifications',
                'order_updates',
                'promotional_emails',
                'security_alerts',
                'profile_visibility',
                'show_email',
                'show_phone',
                'allow_messages',
            ]);
        });
    }
};
