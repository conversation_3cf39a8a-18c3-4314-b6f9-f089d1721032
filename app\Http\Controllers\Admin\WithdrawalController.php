<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Withdrawal;
use App\Models\Vendor;
use App\Services\WithdrawalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WithdrawalController extends Controller
{
    protected $withdrawalService;

    public function __construct(WithdrawalService $withdrawalService)
    {
        $this->withdrawalService = $withdrawalService;
    }

    /**
     * Display a listing of withdrawal requests
     */
    public function index(Request $request)
    {
        $query = Withdrawal::with(['vendor.user']);

        // Filter by status
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Filter by method
        if ($request->has('method') && $request->method !== 'all') {
            $query->where('method', $request->method);
        }

        // Search by vendor name or reference
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('vendor.user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Date range filter
        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $withdrawals = $query->latest()->paginate(20);

        // Get summary statistics
        $stats = [
            'total_pending' => Withdrawal::where('status', 'pending')->count(),
            'total_approved' => Withdrawal::where('status', 'approved')->count(),
            'total_processing' => Withdrawal::where('status', 'processing')->count(),
            'total_completed' => Withdrawal::where('status', 'completed')->count(),
            'total_amount_pending' => Withdrawal::where('status', 'pending')->sum('amount'),
            'total_amount_completed' => Withdrawal::where('status', 'completed')->sum('amount'),
        ];

        return view('admin.withdrawals.index', compact('withdrawals', 'stats'));
    }

    /**
     * Show the specified withdrawal request
     */
    public function show(Withdrawal $withdrawal)
    {
        $withdrawal->load(['vendor.user', 'vendor.wallet.transactions']);
        
        return view('admin.withdrawals.show', compact('withdrawal'));
    }

    /**
     * Approve a withdrawal request
     */
    public function approve(Request $request, Withdrawal $withdrawal)
    {
        if ($withdrawal->status !== 'pending') {
            return redirect()->back()->with('error', 'Only pending withdrawals can be approved.');
        }

        try {
            $withdrawal->update([
                'status' => 'approved',
                'notes' => $request->input('notes', $withdrawal->notes),
                'approved_at' => now(),
                'approved_by' => auth()->id(),
            ]);

            return redirect()->back()->with('success', 'Withdrawal request approved successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to approve withdrawal: ' . $e->getMessage());
        }
    }

    /**
     * Reject a withdrawal request
     */
    public function reject(Request $request, Withdrawal $withdrawal)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        if (!in_array($withdrawal->status, ['pending', 'approved'])) {
            return redirect()->back()->with('error', 'Only pending or approved withdrawals can be rejected.');
        }

        try {
            DB::beginTransaction();

            // If withdrawal was already approved, refund the amount back to vendor wallet
            if ($withdrawal->status === 'approved') {
                $wallet = $withdrawal->vendor->wallet;
                if ($wallet) {
                    $wallet->increment('available_balance', $withdrawal->amount);
                    $wallet->transactions()->create([
                        'type' => 'refund',
                        'amount' => $withdrawal->amount,
                        'status' => 'completed',
                        'description' => "Refund for rejected withdrawal #{$withdrawal->reference_number}",
                        'withdrawal_id' => $withdrawal->id,
                        'balance_after' => $wallet->available_balance,
                    ]);
                }
            }

            $withdrawal->update([
                'status' => 'rejected',
                'notes' => $request->rejection_reason,
                'rejected_at' => now(),
                'rejected_by' => auth()->id(),
            ]);

            DB::commit();

            return redirect()->back()->with('success', 'Withdrawal request rejected successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to reject withdrawal: ' . $e->getMessage());
        }
    }

    /**
     * Process approved withdrawal via Paystack
     */
    public function process(Withdrawal $withdrawal)
    {
        if ($withdrawal->status !== 'approved') {
            return redirect()->back()->with('error', 'Only approved withdrawals can be processed.');
        }

        try {
            $success = $this->withdrawalService->processWithdrawal($withdrawal);
            
            if ($success) {
                return redirect()->back()->with('success', 'Withdrawal processing initiated successfully.');
            } else {
                return redirect()->back()->with('error', 'Failed to process withdrawal. Please check logs for details.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to process withdrawal: ' . $e->getMessage());
        }
    }

    /**
     * Bulk approve withdrawals
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'withdrawal_ids' => 'required|array',
            'withdrawal_ids.*' => 'exists:withdrawals,id'
        ]);

        try {
            $count = Withdrawal::whereIn('id', $request->withdrawal_ids)
                ->where('status', 'pending')
                ->update([
                    'status' => 'approved',
                    'approved_at' => now(),
                    'approved_by' => auth()->id(),
                ]);

            return redirect()->back()->with('success', "{$count} withdrawal requests approved successfully.");
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to approve withdrawals: ' . $e->getMessage());
        }
    }

    /**
     * Export withdrawals to CSV
     */
    public function export(Request $request)
    {
        $query = Withdrawal::with(['vendor.user']);

        // Apply same filters as index
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        if ($request->has('method') && $request->method !== 'all') {
            $query->where('method', $request->method);
        }

        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $withdrawals = $query->latest()->get();

        $filename = 'withdrawals_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($withdrawals) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Reference Number',
                'Vendor Name',
                'Amount',
                'Method',
                'Status',
                'Created At',
                'Processed At',
                'Notes'
            ]);

            // CSV data
            foreach ($withdrawals as $withdrawal) {
                fputcsv($file, [
                    $withdrawal->reference_number,
                    $withdrawal->vendor->user->name ?? 'N/A',
                    number_format($withdrawal->amount, 2),
                    ucfirst($withdrawal->method),
                    ucfirst($withdrawal->status),
                    $withdrawal->created_at->format('Y-m-d H:i:s'),
                    $withdrawal->processed_at ? $withdrawal->processed_at->format('Y-m-d H:i:s') : 'N/A',
                    $withdrawal->notes ?? 'N/A'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
