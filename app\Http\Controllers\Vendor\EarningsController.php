<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Commission;
use App\Models\Withdrawal;
use App\Models\Order;
use App\Models\VendorWallet;
use App\Services\WithdrawalService;
use App\Services\PaystackService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EarningsController extends Controller
{
    protected $withdrawalService;
    protected $paystackService;

    public function __construct(WithdrawalService $withdrawalService, PaystackService $paystackService)
    {
        $this->withdrawalService = $withdrawalService;
        $this->paystackService = $paystackService;
    }

    /**
     * Display the vendor's earnings and commissions.
     */
    public function index(Request $request)
    {
        $vendor = auth()->user()->vendor;

        if (!$vendor) {
            return redirect()->route('vendor.register')->with('error', 'Please complete vendor registration first.');
        }

        // Get or create vendor wallet
        $wallet = $vendor->wallet ?? VendorWallet::create(['vendor_id' => $vendor->id]);
        $period = $request->input('period', 'month'); // Default to monthly view

        // Default date range (current month)
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        // Adjust date range based on period
        switch ($period) {
            case 'week':
                $startDate = Carbon::now()->startOfWeek();
                $endDate = Carbon::now()->endOfWeek();
                break;
            case 'year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;
            case 'all':
                $startDate = Carbon::create(2020, 1, 1); // Platform start date
                $endDate = Carbon::now();
                break;
        }
        
        // If period is set, adjust date range
        if ($period === 'week') {
            $startDate = Carbon::now()->startOfWeek();
            $endDate = Carbon::now()->endOfWeek();
        } elseif ($period === 'year') {
            $startDate = Carbon::now()->startOfYear();
            $endDate = Carbon::now()->endOfYear();
        } elseif ($period === 'all') {
            $startDate = Carbon::createFromTimestamp(0); // Beginning of time
            $endDate = Carbon::now(); // Now
        } elseif ($period === 'custom' && $request->filled('start_date') && $request->filled('end_date')) {
            $startDate = Carbon::parse($request->input('start_date'))->startOfDay();
            $endDate = Carbon::parse($request->input('end_date'))->endOfDay();
        }
        
        // Get sales data
        $salesData = $vendor->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->where('orders.status', '!=', 'cancelled')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('SUM(order_items.price_at_purchase * order_items.quantity) as total_sales')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();
        
        // Get total sales in the period
        $totalSales = $salesData->sum('total_sales');
        
        // Get commissions
        $commissions = Commission::where('vendor_id', $vendor->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        
        // Get paid and pending commissions
        $paidCommissions = Commission::where('vendor_id', $vendor->id)
            ->where('status', 'paid')
            ->sum('amount');
            
        $pendingCommissions = Commission::where('vendor_id', $vendor->id)
            ->where('status', 'pending')
            ->sum('amount');
        
        // Calculate earnings (sales minus commissions)
        $totalCommissions = $commissions->sum('amount');
        $totalEarnings = $totalSales - $totalCommissions;
        
        // Format data for the chart
        $chartDates = $salesData->pluck('date');
        $chartSales = $salesData->pluck('total_sales');
        
        return view('vendor.earnings.index', compact(
            'period', 'totalSales', 'totalCommissions', 'totalEarnings',
            'paidCommissions', 'pendingCommissions', 'commissions',
            'chartDates', 'chartSales', 'startDate', 'endDate'
        ));
    }
    
    /**
     * Request a withdrawal of available funds.
     */
    public function withdraw(Request $request)
    {
        $vendor = auth()->user()->vendor;

        if (!$vendor) {
            return redirect()->route('vendor.register')->with('error', 'Please complete vendor registration first.');
        }

        // Validate the request
        $request->validate([
            'amount' => 'required|numeric|min:1000', // Minimum withdrawal of ₦1,000
            'withdrawal_method' => 'required|in:bank_transfer,paystack',
            'bank_name' => 'required_if:withdrawal_method,bank_transfer',
            'bank_code' => 'required_if:withdrawal_method,bank_transfer',
            'account_name' => 'required_if:withdrawal_method,bank_transfer',
            'account_number' => 'required_if:withdrawal_method,bank_transfer',
            'paystack_email' => 'required_if:withdrawal_method,paystack|email',
            'notes' => 'nullable|string|max:500'
        ]);

        try {
            $withdrawalData = [
                'amount' => $request->amount,
                'method' => $request->withdrawal_method,
                'notes' => $request->notes,
            ];

            // Add method-specific details
            if ($request->withdrawal_method === 'bank_transfer') {
                $withdrawalData = array_merge($withdrawalData, [
                    'bank_name' => $request->bank_name,
                    'bank_code' => $request->bank_code,
                    'account_name' => $request->account_name,
                    'account_number' => $request->account_number,
                ]);
            } elseif ($request->withdrawal_method === 'paystack') {
                $withdrawalData['paystack_email'] = $request->paystack_email;
            }

            $withdrawal = $this->withdrawalService->createWithdrawalRequest($vendor, $withdrawalData);

            return redirect()->back()->with('success',
                "Withdrawal request #{$withdrawal->reference_number} submitted successfully. It will be reviewed and processed within 24-48 hours."
            );

        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Get banks list for withdrawal form
     */
    public function getBanks()
    {
        try {
            $banks = $this->paystackService->getBanks();
            return response()->json($banks);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch banks'], 500);
        }
    }

    /**
     * Verify account number
     */
    public function verifyAccount(Request $request)
    {
        $request->validate([
            'account_number' => 'required|string|size:10',
            'bank_code' => 'required|string'
        ]);

        try {
            $verification = $this->paystackService->verifyAccountNumber(
                $request->account_number,
                $request->bank_code
            );

            return response()->json($verification);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Account verification failed'], 500);
        }
    }
}
