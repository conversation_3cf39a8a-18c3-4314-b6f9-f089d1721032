<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-white text-center py-4">
                    <h3 class="fw-bold mb-1">Vendor Registration</h3>
                    <p class="text-muted mb-0">Join our marketplace and start selling</p>
                </div>
                
                <div class="card-body p-4">
                    @if (session()->has('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if (session()->has('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <form wire:submit="register" enctype="multipart/form-data">
                        <!-- Personal Information Section -->
                        <div class="mb-4">
                            <h5 class="fw-bold mb-3 text-primary">Personal Information</h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" wire:model="name" class="form-control @error('name') is-invalid @enderror" id="name" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" wire:model="email" class="form-control @error('email') is-invalid @enderror" id="email" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                    <input type="password" wire:model="password" class="form-control @error('password') is-invalid @enderror" id="password" required>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                    <input type="password" wire:model="password_confirmation" class="form-control @error('password_confirmation') is-invalid @enderror" id="password_confirmation" required>
                                    @error('password_confirmation')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                <input type="tel" wire:model="phone" class="form-control @error('phone') is-invalid @enderror" id="phone" required>
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Business Information Section -->
                        <div class="mb-4">
                            <h5 class="fw-bold mb-3 text-primary">Business Information</h5>
                            
                            <div class="mb-3">
                                <label for="business_name" class="form-label">Business Name <span class="text-danger">*</span></label>
                                <input type="text" wire:model="business_name" class="form-control @error('business_name') is-invalid @enderror" id="business_name" required>
                                @error('business_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="business_description" class="form-label">Business Description <span class="text-danger">*</span></label>
                                <textarea wire:model="business_description" class="form-control @error('business_description') is-invalid @enderror" id="business_description" rows="3" required></textarea>
                                @error('business_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="business_address" class="form-label">Business Address <span class="text-danger">*</span></label>
                                <textarea wire:model="business_address" class="form-control @error('business_address') is-invalid @enderror" id="business_address" rows="2" required></textarea>
                                @error('business_address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="city" class="form-label">City <span class="text-danger">*</span></label>
                                    <input type="text" wire:model="city" class="form-control @error('city') is-invalid @enderror" id="city" required>
                                    @error('city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="state" class="form-label">State <span class="text-danger">*</span></label>
                                    <input type="text" wire:model="state" class="form-control @error('state') is-invalid @enderror" id="state" required>
                                    @error('state')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="country" class="form-label">Country <span class="text-danger">*</span></label>
                                    <input type="text" wire:model="country" class="form-control @error('country') is-invalid @enderror" id="country" required>
                                    @error('country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Document Upload Section -->
                        <div class="mb-4">
                            <h5 class="fw-bold mb-3 text-primary">Document Upload</h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="logo" class="form-label">Business Logo</label>
                                    <input type="file" wire:model="logo" class="form-control @error('logo') is-invalid @enderror" id="logo" accept="image/*">
                                    @error('logo')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Optional. Max size: 2MB. Formats: JPG, PNG, GIF</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="id_document" class="form-label">ID Document <span class="text-danger">*</span></label>
                                    <input type="file" wire:model="id_document" class="form-control @error('id_document') is-invalid @enderror" id="id_document" required>
                                    @error('id_document')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Required. Max size: 5MB. Formats: JPG, PNG, PDF</div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="business_document" class="form-label">Business Registration Document</label>
                                <input type="file" wire:model="business_document" class="form-control @error('business_document') is-invalid @enderror" id="business_document">
                                @error('business_document')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Optional. Max size: 5MB. Formats: JPG, PNG, PDF</div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input wire:model="terms" class="form-check-input @error('terms') is-invalid @enderror" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" class="text-decoration-none">Terms and Conditions</a> and <a href="#" class="text-decoration-none">Privacy Policy</a> <span class="text-danger">*</span>
                                </label>
                                @error('terms')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-primary w-100 py-2" wire:loading.attr="disabled">
                            <span wire:loading.remove>Register as Vendor</span>
                            <span wire:loading>
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Registering...
                            </span>
                        </button>
                    </form>

                    <div class="text-center mt-4">
                        <p class="text-muted">Already have an account? 
                            <a href="{{ route('login') }}" class="text-decoration-none" wire:navigate>Sign in</a>
                        </p>
                        <p class="text-muted">Want to register as a customer? 
                            <a href="{{ route('register') }}" class="text-decoration-none" wire:navigate>Customer Registration</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
