<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rule;

class ProfileController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the profile settings page
     */
    public function index()
    {
        $user = Auth::user();
        return view('profile.index', compact('user'));
    }

    /**
     * Update profile information
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'date_of_birth' => ['nullable', 'date', 'before:today'],
            'gender' => ['nullable', 'in:male,female,other'],
            'bio' => ['nullable', 'string', 'max:500'],
            'avatar' => ['nullable', 'image', 'mimes:jpg,jpeg,png,gif', 'max:2048'],
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $avatarPath;
        }

        // Update user data
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'date_of_birth' => $request->date_of_birth,
            'gender' => $request->gender,
            'bio' => $request->bio,
        ]);

        // Handle email verification if email changed
        if ($user->wasChanged('email')) {
            $user->email_verified_at = null;
            $user->save();
            $user->sendEmailVerificationNotification();
        }

        return redirect()->back()->with('success', 'Profile updated successfully!');
    }

    /**
     * Update password
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user = Auth::user();
        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->back()->with('success', 'Password updated successfully!');
    }

    /**
     * Update notification preferences
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'email_notifications' => ['boolean'],
            'order_updates' => ['boolean'],
            'promotional_emails' => ['boolean'],
            'security_alerts' => ['boolean'],
        ]);

        $user->update([
            'email_notifications' => $request->boolean('email_notifications'),
            'order_updates' => $request->boolean('order_updates'),
            'promotional_emails' => $request->boolean('promotional_emails'),
            'security_alerts' => $request->boolean('security_alerts'),
        ]);

        return redirect()->back()->with('success', 'Notification preferences updated successfully!');
    }

    /**
     * Update privacy settings
     */
    public function updatePrivacy(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'profile_visibility' => ['required', 'in:public,private'],
            'show_email' => ['boolean'],
            'show_phone' => ['boolean'],
            'allow_messages' => ['boolean'],
        ]);

        $user->update([
            'profile_visibility' => $request->profile_visibility,
            'show_email' => $request->boolean('show_email'),
            'show_phone' => $request->boolean('show_phone'),
            'allow_messages' => $request->boolean('allow_messages'),
        ]);

        return redirect()->back()->with('success', 'Privacy settings updated successfully!');
    }

    /**
     * Delete user account
     */
    public function deleteAccount(Request $request)
    {
        $request->validate([
            'password' => ['required', 'current_password'],
            'confirmation' => ['required', 'in:DELETE'],
        ]);

        $user = Auth::user();

        // Delete avatar if exists
        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
        }

        // Logout and delete user
        Auth::logout();
        $user->delete();

        return redirect()->route('home')->with('success', 'Your account has been deleted successfully.');
    }

    /**
     * Remove avatar
     */
    public function removeAvatar()
    {
        $user = Auth::user();

        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
        }

        $user->update(['avatar' => null]);

        return redirect()->back()->with('success', 'Avatar removed successfully!');
    }

    /**
     * Download user data (GDPR compliance)
     */
    public function downloadData()
    {
        $user = Auth::user();
        
        $userData = [
            'personal_information' => [
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'date_of_birth' => $user->date_of_birth,
                'gender' => $user->gender,
                'bio' => $user->bio,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
            ],
            'orders' => $user->orders()->with('items.product')->get()->toArray(),
            'wishlist' => $user->wishlistItems()->with('product')->get()->toArray(),
            'reviews' => $user->reviews()->with('product')->get()->toArray(),
        ];

        $fileName = 'user_data_' . $user->id . '_' . now()->format('Y-m-d_H-i-s') . '.json';
        
        return response()->json($userData)
            ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"')
            ->header('Content-Type', 'application/json');
    }

    /**
     * Get user activity log
     */
    public function activityLog()
    {
        $user = Auth::user();
        
        // Get recent activities
        $activities = collect([
            // Recent orders
            ...$user->orders()->latest()->take(10)->get()->map(function ($order) {
                return [
                    'type' => 'order',
                    'description' => "Placed order #{$order->order_number}",
                    'date' => $order->created_at,
                    'icon' => 'shopping-bag',
                    'color' => 'primary'
                ];
            }),
            
            // Recent wishlist additions
            ...$user->wishlistItems()->latest()->take(5)->get()->map(function ($item) {
                return [
                    'type' => 'wishlist',
                    'description' => "Added {$item->product->name} to wishlist",
                    'date' => $item->created_at,
                    'icon' => 'heart',
                    'color' => 'danger'
                ];
            }),
            
            // Recent reviews
            ...$user->reviews()->latest()->take(5)->get()->map(function ($review) {
                return [
                    'type' => 'review',
                    'description' => "Reviewed {$review->product->name}",
                    'date' => $review->created_at,
                    'icon' => 'star',
                    'color' => 'warning'
                ];
            }),
        ])->sortByDesc('date')->take(20);

        return view('profile.activity', compact('activities'));
    }
}
