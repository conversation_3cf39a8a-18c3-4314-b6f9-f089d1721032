<?php

namespace App\Livewire\Auth;

use App\Models\User;
use App\Models\Role;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('components.layouts.auth-bootstrap')]
class Register extends Component
{
    public string $name = '';

    public string $email = '';

    public string $password = '';

    public string $password_confirmation = '';

        public $registering = false;

    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $this->registering = true;

        try {
            $validated = $this->validate([
                'name' => ['required', 'string', 'max:255'],
                'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
                'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            ]);

            $validated['password'] = Hash::make($validated['password']);

            // Assign customer role by default
            $customerRole = Role::where('name', 'customer')->first();
            if (!$customerRole) {
                throw new \Exception('Customer role not found. Please contact administrator.');
            }

            $validated['role_id'] = $customerRole->id;

            // Create the user
            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => $validated['password'],
                'role_id' => $validated['role_id']
            ]);

            event(new Registered($user));

            Auth::login($user);

            session()->flash('success', 'Registration successful! Welcome to Brandify.');

        } catch (\Exception $e) {
            session()->flash('error', 'Registration failed: ' . $e->getMessage());
            $this->registering = false;
            return;
        }

        $this->registering = false;

        // Redirect to appropriate dashboard based on role
        $redirectRoute = auth()->user()->isVendor() ? 'vendor.dashboard' : 'dashboard';
        $this->redirect(route($redirectRoute, absolute: false), navigate: true);
    }
}
