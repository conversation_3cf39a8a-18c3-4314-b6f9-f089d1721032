<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('platform_incomes', function (Blueprint $table) {
            $table->id();
            $table->enum('type', [
                'commission',
                'subscription',
                'payment_fee',
                'withdrawal_fee',
                'shipping_fee',
                'listing_fee',
                'other'
            ])->comment('Type of income');
            $table->decimal('amount', 15, 2)->comment('Income amount');
            $table->unsignedBigInteger('source_id')->nullable()->comment('ID of the source record');
            $table->string('source_type')->nullable()->comment('Type of the source model');
            $table->string('description')->comment('Description of the income');
            $table->json('metadata')->nullable()->comment('Additional income data');
            $table->timestamp('recorded_at')->comment('When the income was recorded');
            $table->timestamps();

            // Indexes
            $table->index(['type', 'recorded_at']);
            $table->index(['source_id', 'source_type']);
            $table->index(['recorded_at']);
            $table->index(['type', 'amount']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('platform_incomes');
    }
};
