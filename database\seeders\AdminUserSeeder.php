<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main admin user
        $mainAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );

        if ($mainAdmin->wasRecentlyCreated) {
            $this->command->info('✅ Main admin user created: <EMAIL> (password: admin123)');
        } else {
            $this->command->info('ℹ️  Main admin user already exists: <EMAIL>');
        }

        // Create secondary admin user for testing
        $testAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('testadmin123'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );

        if ($testAdmin->wasRecentlyCreated) {
            $this->command->info('✅ Test admin user created: <EMAIL> (password: testadmin123)');
        } else {
            $this->command->info('ℹ️  Test admin user already exists: <EMAIL>');
        }

        // Create super admin user
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('superadmin123'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );

        if ($superAdmin->wasRecentlyCreated) {
            $this->command->info('✅ Super admin user created: <EMAIL> (password: superadmin123)');
        } else {
            $this->command->info('ℹ️  Super admin user already exists: <EMAIL>');
        }

        $this->command->info('');
        $this->command->info('🔐 Admin Users Summary:');
        $this->command->info('   • <EMAIL> (password: admin123)');
        $this->command->info('   • <EMAIL> (password: testadmin123)');
        $this->command->info('   • <EMAIL> (password: superadmin123)');
        $this->command->info('');
        $this->command->info('💡 You can now login to the admin panel at /admin/dashboard');
    }
}
