<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'vendor_id',
        'order_number',
        'total_amount',
        'subtotal',
        'tax',
        'shipping',
        'total', // Keep for backward compatibility
        'status',
        'payment_status',
        'payment_method',
        'payment_gateway_reference',
        'payment_details',
        'shipbubble_shipment_id',
        'tracking_number',
        'shipping_carrier',
        'shipping_address',
        'shipping_name',
        'shipping_city',
        'shipping_state',
        'shipping_postal_code',
        'shipping_country',
        'shipping_phone',
        'shipping_method',
        'shipping_latitude',
        'shipping_longitude',
        'billing_address',
        'notes',
        'shipped_at',
        'delivered_at',
    ];
    
    protected $casts = [
        'total_amount' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'tax' => 'decimal:2',
        'shipping' => 'decimal:2',
        'total' => 'decimal:2',
        'shipping_address' => 'array',
        'billing_address' => 'array',
        'payment_details' => 'array',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
    ];
    
    /**
     * Boot function to handle order events
     */
    protected static function boot()
    {
        parent::boot();
        
        // Auto-generate order number when creating a new order
        static::creating(function ($order) {
            if (empty($order->order_number)) {
                $order->order_number = 'ORD-' . uniqid();
            }
        });

        // Increment vendor order count when order is created
        static::created(function ($order) {
            if ($order->vendor_id) {
                $vendor = $order->vendor;
                if ($vendor) {
                    $vendor->incrementOrderCount();
                }
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }
    
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }
    
    public function payment()
    {
        return $this->hasOne(Payment::class);
    }
    
    public function isPending()
    {
        return $this->status === 'pending';
    }
    
    public function isProcessing()
    {
        return $this->status === 'processing';
    }
    
    public function isCompleted()
    {
        return $this->status === 'completed';
    }
    
    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }
    
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }
}
