@extends('layouts.app')

@section('content')
<div class="container py-5">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-lg-3 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="text-center p-4 border-bottom">
                        <div class="position-relative d-inline-block">
                            <img src="{{ $user->avatar_url }}" 
                                 alt="{{ $user->name }}" 
                                 class="rounded-circle mb-3"
                                 style="width: 80px; height: 80px; object-fit: cover;">
                        </div>
                        <h5 class="fw-bold mb-1">{{ $user->name }}</h5>
                        <p class="text-muted small mb-0">{{ $user->email }}</p>
                        @if($user->isVendor())
                            <span class="badge bg-primary mt-2">Vendor</span>
                        @else
                            <span class="badge bg-secondary mt-2">Customer</span>
                        @endif
                    </div>
                    
                    <div class="list-group list-group-flush">
                        <a href="#profile" class="list-group-item list-group-item-action border-0 profile-tab active" data-tab="profile">
                            <i class="fas fa-user me-3"></i>Profile Information
                        </a>
                        <a href="#security" class="list-group-item list-group-item-action border-0 profile-tab" data-tab="security">
                            <i class="fas fa-lock me-3"></i>Security
                        </a>
                        <a href="#notifications" class="list-group-item list-group-item-action border-0 profile-tab" data-tab="notifications">
                            <i class="fas fa-bell me-3"></i>Notifications
                        </a>
                        <a href="#privacy" class="list-group-item list-group-item-action border-0 profile-tab" data-tab="privacy">
                            <i class="fas fa-shield-alt me-3"></i>Privacy
                        </a>
                        <a href="#activity" class="list-group-item list-group-item-action border-0 profile-tab" data-tab="activity">
                            <i class="fas fa-history me-3"></i>Activity Log
                        </a>
                        <a href="#data" class="list-group-item list-group-item-action border-0 profile-tab" data-tab="data">
                            <i class="fas fa-download me-3"></i>My Data
                        </a>
                        <div class="border-top mt-2 pt-2">
                            <a href="#delete" class="list-group-item list-group-item-action border-0 text-danger profile-tab" data-tab="delete">
                                <i class="fas fa-trash me-3"></i>Delete Account
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Profile Information Tab -->
            <div class="tab-content active" id="profile-content">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="fw-bold mb-0">Profile Information</h5>
                        <p class="text-muted small mb-0">Update your account's profile information and email address.</p>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PATCH')
                            
                            <!-- Avatar Upload -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">Profile Picture</label>
                                </div>
                                <div class="col-md-9">
                                    <div class="d-flex align-items-center">
                                        <img src="{{ $user->avatar_url }}" 
                                             alt="{{ $user->name }}" 
                                             class="rounded-circle me-3"
                                             style="width: 60px; height: 60px; object-fit: cover;"
                                             id="avatar-preview">
                                        <div>
                                            <input type="file" class="form-control mb-2" id="avatar" name="avatar" accept="image/*">
                                            <small class="text-muted">JPG, PNG, GIF up to 2MB</small>
                                            @if($user->avatar)
                                                <div class="mt-2">
                                                    <a href="{{ route('profile.remove-avatar') }}" 
                                                       class="btn btn-sm btn-outline-danger"
                                                       onclick="return confirm('Remove profile picture?')">
                                                        Remove Picture
                                                    </a>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    @error('avatar')
                                        <div class="text-danger small mt-1">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label fw-bold">Full Name</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label fw-bold">Email Address</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if(!$user->email_verified_at)
                                        <small class="text-warning">Email not verified</small>
                                    @endif
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="phone" class="form-label fw-bold">Phone Number</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="date_of_birth" class="form-label fw-bold">Date of Birth</label>
                                    <input type="date" class="form-control @error('date_of_birth') is-invalid @enderror" 
                                           id="date_of_birth" name="date_of_birth" 
                                           value="{{ old('date_of_birth', $user->date_of_birth?->format('Y-m-d')) }}">
                                    @error('date_of_birth')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="gender" class="form-label fw-bold">Gender</label>
                                    <select class="form-select @error('gender') is-invalid @enderror" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="male" {{ old('gender', $user->gender) === 'male' ? 'selected' : '' }}>Male</option>
                                        <option value="female" {{ old('gender', $user->gender) === 'female' ? 'selected' : '' }}>Female</option>
                                        <option value="other" {{ old('gender', $user->gender) === 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    @error('gender')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="bio" class="form-label fw-bold">Bio</label>
                                <textarea class="form-control @error('bio') is-invalid @enderror" 
                                          id="bio" name="bio" rows="3" 
                                          placeholder="Tell us about yourself...">{{ old('bio', $user->bio) }}</textarea>
                                <small class="text-muted">Maximum 500 characters</small>
                                @error('bio')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Security Tab -->
            <div class="tab-content" id="security-content">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="fw-bold mb-0">Security Settings</h5>
                        <p class="text-muted small mb-0">Update your password and security preferences.</p>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('profile.update-password') }}" method="POST">
                            @csrf
                            @method('PATCH')
                            
                            <div class="mb-3">
                                <label for="current_password" class="form-label fw-bold">Current Password</label>
                                <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                       id="current_password" name="current_password" required>
                                @error('current_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label fw-bold">New Password</label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password" required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-4">
                                <label for="password_confirmation" class="form-label fw-bold">Confirm New Password</label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation" required>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-lock me-2"></i>Update Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Other tabs will be loaded via AJAX or included here -->
            <!-- For brevity, I'll create separate view files for other tabs -->
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.profile-tab {
    transition: all 0.3s ease;
}

.profile-tab:hover {
    background-color: #f8f9fa;
}

.profile-tab.active {
    background-color: #007bff;
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

#avatar-preview {
    transition: all 0.3s ease;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    document.querySelectorAll('.profile-tab').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all tabs
            document.querySelectorAll('.profile-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Show corresponding content
            const tabId = this.dataset.tab;
            const content = document.getElementById(tabId + '-content');
            if (content) {
                content.classList.add('active');
            }
        });
    });

    // Avatar preview
    document.getElementById('avatar').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('avatar-preview').src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endpush
